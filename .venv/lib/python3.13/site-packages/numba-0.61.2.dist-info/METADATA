Metadata-Version: 2.1
Name: numba
Version: 0.61.2
Summary: compiling Python code using LLVM
Home-page: https://numba.pydata.org
License: BSD
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Topic :: Software Development :: Compilers
Requires-Python: >=3.10
License-File: LICENSE
License-File: LICENSES.third-party
Requires-Dist: llvmlite<0.45,>=0.44.0dev0
Requires-Dist: numpy<2.3,>=1.24

*****
Numba
*****

.. image:: https://badges.gitter.im/numba/numba.svg
   :target: https://gitter.im/numba/numba?utm_source=badge&utm_medium=badge&utm_campaign=pr-badge
   :alt: Gitter

.. image:: https://img.shields.io/badge/discuss-on%20discourse-blue
   :target: https://numba.discourse.group/
   :alt: Discourse

.. image:: https://zenodo.org/badge/3659275.svg
   :target: https://zenodo.org/badge/latestdoi/3659275
   :alt: Zenodo DOI

.. image:: https://img.shields.io/pypi/v/numba.svg
   :target: https://pypi.python.org/pypi/numba/
   :alt: PyPI

.. image:: https://dev.azure.com/numba/numba/_apis/build/status/numba.numba?branchName=main
    :target: https://dev.azure.com/numba/numba/_build/latest?definitionId=1?branchName=main
    :alt: Azure Pipelines

A Just-In-Time Compiler for Numerical Functions in Python
#########################################################

Numba is an open source, NumPy-aware optimizing compiler for Python sponsored
by Anaconda, Inc.  It uses the LLVM compiler project to generate machine code
from Python syntax.

Numba can compile a large subset of numerically-focused Python, including many
NumPy functions.  Additionally, Numba has support for automatic
parallelization of loops, generation of GPU-accelerated code, and creation of
ufuncs and C callbacks.

For more information about Numba, see the Numba homepage:
https://numba.pydata.org and the online documentation:
https://numba.readthedocs.io/en/stable/index.html

Installation
============

Please follow the instructions:

https://numba.readthedocs.io/en/stable/user/installing.html

Demo
====

Please have a look and the demo notebooks via the mybinder service:

https://mybinder.org/v2/gh/numba/numba-examples/master?filepath=notebooks

Contact
=======

Numba has a discourse forum for discussions:

* https://numba.discourse.group

