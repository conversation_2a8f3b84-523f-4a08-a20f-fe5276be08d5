scipy-1.15.3.dist-info/WHEEL,sha256=Z6suL4LoFGKsAj0u-1H2KPWIAj9PBf-rOSI4euKUzl8,93
scipy-1.15.3.dist-info/LICENSE.txt,sha256=dVLSVmtwmJgEVLt4gzhdIn-7TfodYizFyFw-ij8jYG8,46848
scipy-1.15.3.dist-info/METADATA,sha256=mKZxxnIUMtYDBlk823kDR3Rz07cSVExmZILtEThk9nY,61959
scipy/conftest.py,sha256=Q3DbWzqWdFt8hkq16Bbg4MQ8WaxgKuhhKp6XEEZ8bWw,22027
scipy/version.py,sha256=onH968TxWuVmzTyMqWhrz1LhI91dIlSxYYrL2dQpLrw,318
scipy/__init__.py,sha256=GFkTqhB1Evr9XPid_UUqhxm0Wm66gz4tzuLL_Ri0u-U,4153
scipy/_distributor_init.py,sha256=zJThN3Fvof09h24804pNDPd2iN-lCHV3yPlZylSefgQ,611
scipy/__config__.py,sha256=mBbuA0rAb3bgTsm7HuSfphf52jKmUuPKOhvNpy5Cptw,4946
scipy/odr/_add_newdocs.py,sha256=GeWL4oIb2ydph_K3qCjiIbPCM3QvpwP5EZwEJVOzJrQ,1128
scipy/odr/models.py,sha256=Fcdj-P9rJ_B-Ct8bh3RrusnapeHLysVaDsM26Q8fHFo,590
scipy/odr/_odrpack.py,sha256=n30DVx78Oh0zDItjKdqDaJpiXSyVPqHYGk63a1-5NZg,42496
scipy/odr/odrpack.py,sha256=OlRlBxKlzp5VDi2fnnA-Jdl6G0chDt95JNCvJYg2czs,632
scipy/odr/__init__.py,sha256=CErxMJ0yBfu_cvCoKJMu9WjqUaohLIqqf228Gm9XWJI,4325
scipy/odr/__odrpack.cpython-313-darwin.so,sha256=CxgoEniVQMo42liRIalbuBHDmo7AAFbQDH3eawa9-lw,240608
scipy/odr/_models.py,sha256=tfOLgqnV4LR3VKi7NAg1g1Jp_Zw8lG_PA5BHwU_pTH0,7800
scipy/odr/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/odr/tests/test_odr.py,sha256=MkCfBdQvbCtiLgDFaIAp0jclwj2mIhwgL3J0Asvq31Q,22079
scipy/misc/__init__.py,sha256=dVfULY959nFwpl5NCxyCpiHyNcSNaR7HYOg7QU21a5s,135
scipy/misc/common.py,sha256=nAGQOVR9ZEAb703uhOVQZqf-z0iCM4EDhbHK4_h_Tdc,142
scipy/misc/doccer.py,sha256=wHbpGV8todadz6MIzJHalDfRjiKI164qs6iMcHgsVu0,142
scipy/cluster/_optimal_leaf_ordering.cpython-313-darwin.so,sha256=i4ty4ukT5P4bmDfGdKTPcCbzZ26a3g5uXheV1D8r_Kw,246112
scipy/cluster/vq.py,sha256=wa5bcXyigz2XiCNOu91qCuw0fvreoKSbHaRP0QQbOs4,30548
scipy/cluster/__init__.py,sha256=pgzWiWR5smQ3rwud2dhnLn6dpkD5lju_moElQp_zhoE,880
scipy/cluster/hierarchy.py,sha256=gXomjlief0U5nn-lYGxONKA6GMQB6Xtl0PAqJKm9e_E,149078
scipy/cluster/_vq.cpython-313-darwin.so,sha256=k0t3zJQ1UVT3Mgz5Vz5sdAVfGMErB0s4FEh7Jvhw0qM,129128
scipy/cluster/_hierarchy.cpython-313-darwin.so,sha256=nsGaSLRNtOI0iJdqsfm1fuMq50GgO8cTHY2hdSWB2UM,333328
scipy/cluster/tests/hierarchy_test_data.py,sha256=7syUYdIaDVr7hgvMliX0CW4386utjBJn1DOgX0USXls,6850
scipy/cluster/tests/test_disjoint_set.py,sha256=EuHGBE3ZVEMnWFbCn8tjI-_6CWrNXfpnv5bUBa9qhWI,5525
scipy/cluster/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/cluster/tests/test_vq.py,sha256=zzM7GmiApkd3fuGYv9405vU9tNNMiFVTqHcvh2phafs,18973
scipy/cluster/tests/test_hierarchy.py,sha256=t4pjYeKNvovBnotlUxX-m1RMBdTSVYvHslWPQ9zjCzc,52109
scipy/ndimage/_ni_support.py,sha256=weYLkgApaf0WG54dksxJnFEY2ToCT9O3XNP4d4pppFM,5308
scipy/ndimage/_delegators.py,sha256=NBf6hkZ7pyrELlhUpP-CvuvBPEgO77FgAfhD38KEk-Q,9256
scipy/ndimage/interpolation.py,sha256=GHYvxCyQsLfKtNUc8AUN_vqmBhmAPwNnxm2-VpFMayk,664
scipy/ndimage/_nd_image.cpython-313-darwin.so,sha256=wm8DQEPTrupwIwnO9ZAYIa7Syyx2p8wUBAEcTWNFqoM,156160
scipy/ndimage/_measurements.py,sha256=eyBWnB0x1CxseFOMPXkfpuu48nhkMuK24hZPBla2wVs,56113
scipy/ndimage/_ni_docstrings.py,sha256=TxAEkoC5ysA5JuK8IM2xoq60yddVWqOXsmxYXIr-4_E,8542
scipy/ndimage/__init__.py,sha256=w4dCQCzsFmzAs7xF18MCTf5ld8HdIFfZjoRxuLQeqwg,5154
scipy/ndimage/_ni_label.cpython-313-darwin.so,sha256=biVb4RVyu3XIV0Txqw1SRNyJvViNEGw9OQdDVqEBrD0,331616
scipy/ndimage/morphology.py,sha256=yFWSo7o_7PuYq61WGQOCIgMppneNLxqhJocyN0bMsVA,965
scipy/ndimage/_ctest.cpython-313-darwin.so,sha256=GP5KvCmqLgv5MV0AgTufGKvoVftOHmsopXCtvX_76uo,51120
scipy/ndimage/_support_alternative_backends.py,sha256=G9J6cBRmZ0VFkAQ72uGdsiQ9-4ZlqTZ4KsX8cs_QZXg,2603
scipy/ndimage/_ndimage_api.py,sha256=S8DBRWydSRfAz-ZlHSMeCSbjYGgCLioa9_Q2VXGeC_g,586
scipy/ndimage/fourier.py,sha256=gnifi4S_Epyu4DpNsebz4A5BKzBWoGf11FkXWeXsoqY,599
scipy/ndimage/_fourier.py,sha256=SoAYRx7ax7Tv51MyYzDlZ3fN682x4T6N8yReX2La4-I,11266
scipy/ndimage/_filters.py,sha256=6CH71a4VDcn9thauWiE1BJBOVBb-vN5CFznz_lJ2nAw,70982
scipy/ndimage/_rank_filter_1d.cpython-313-darwin.so,sha256=1CC5Jvnr2d-vAcjcRGCkTc6p6widSwpBfry-r442N68,51896
scipy/ndimage/_morphology.py,sha256=LF91gKJcHIWoD9ath_9-Y7HgUwQbA0ELqgVYvm1YAWA,100762
scipy/ndimage/filters.py,sha256=cAv2zezrTJEm9JzKPV_pmXzZcgczCK_VaYJ4mdNW3FM,976
scipy/ndimage/measurements.py,sha256=xdSs52Y5RjURLP710iGURXWQFeS3ok4WjoYufKh9OeA,788
scipy/ndimage/_cytest.cpython-313-darwin.so,sha256=kql6A2C-QcUL0XW--iFE1EcPafeiBautgyVo8Ye39uw,96656
scipy/ndimage/_interpolation.py,sha256=Zlb4ZRJbTOrf21dedO28GHTXA0Kh9hMCDWBdGvRbco4,36670
scipy/ndimage/tests/test_interpolation.py,sha256=g-58BrUxEaje9cOWWWRMQDSMcNFTrhWBFEUdTZxzAy0,60681
scipy/ndimage/tests/test_datatypes.py,sha256=TYMiGyBcdOq3KVLzvjZPjerD1EXonyHFQYBLTWDwN7o,2819
scipy/ndimage/tests/test_measurements.py,sha256=JzF8phts7W0xQSRJTo59JSe0voOW5MIxqkbCCRTqkiE,58874
scipy/ndimage/tests/test_c_api.py,sha256=7Gv-hR91MWpiGQ32yjXIBjFytuaYLqz3wYiCXcC8ZSk,3738
scipy/ndimage/tests/test_splines.py,sha256=uAtDEgjNoaqfIk3QGfDfD33XK5_R0WyGgsCUCS3j7P4,2557
scipy/ndimage/tests/test_morphology.py,sha256=bi-c1tjMCgqQagW0Izuv86KO7p1uuFPFjiDUfDM3nIU,128720
scipy/ndimage/tests/__init__.py,sha256=GbIXCsLtZxgmuisjxfFsd3pj6-RQhmauc6AVy6sybDc,314
scipy/ndimage/tests/dots.png,sha256=sgtW-tx0ccBpTT6BSNniioPXlnusFr-IUglK_qOVBBQ,2114
scipy/ndimage/tests/test_ni_support.py,sha256=fcMPR9wmtOePd9eKg1ksGgolmKqVO2xboHsYOd4mC1I,2511
scipy/ndimage/tests/test_filters.py,sha256=_tOMm5NItaTEoY3hqEDpXYflp6bpkosalSa3evzhwjA,125491
scipy/ndimage/tests/test_fourier.py,sha256=2PL6aLDczM65NwUk7YTXXdjskLJmDCgpVD-xTHr55bo,7766
scipy/ndimage/tests/data/label_strels.txt,sha256=AU2FUAg0WghfvnPDW6lhMB1kpNdfv3coCR8blcRNBJ8,252
scipy/ndimage/tests/data/label_results.txt,sha256=Cf2_l7FCWNjIkyi-XU1MaGzmLnf2J7NK2SZ_10O-8d0,4309
scipy/ndimage/tests/data/label_inputs.txt,sha256=JPbEnncwUyhlAAv6grN8ysQW9w9M7ZSIn_NPopqU7z4,294
scipy/linalg/decomp_qr.py,sha256=EJNpu6lSa36Eo-e4rbYu5kDlRTMse2mmGul_PLRFXHs,567
scipy/linalg/_decomp_update.cpython-313-darwin.so,sha256=E9ALSxsTDOMpVEa1hps9JKJdrQ7uU7yyY89BpDgW8wo,287384
scipy/linalg/_matfuncs_inv_ssq.py,sha256=8dL7xD6DU8D4h2YyHcYjRhZQvv1pSOEzMuKlGP6zonw,28095
scipy/linalg/misc.py,sha256=uxpR80jJ5w5mslplWlL6tIathas8mEXvRIwDXYMcTOk,592
scipy/linalg/_sketches.py,sha256=6XwvmXh2zHjUFFsTYmoBYzhAUfZG2hwtdKR-YOzcDDQ,6117
scipy/linalg/decomp_cholesky.py,sha256=1g45oc115ZZR3CfMW1bCPseF5ATz4Xf6Ih26NRqyjfs,649
scipy/linalg/_decomp_cholesky.py,sha256=pk7_zuMkd-q-8AHyrNpm0wDof4-DeWiCFA3ESBkvLSQ,13721
scipy/linalg/_testutils.py,sha256=IWA5vvdZ8yaHeXo2IxpQLqG9q54YIomHscYs85q9pd0,1807
scipy/linalg/_decomp_interpolative.cpython-313-darwin.so,sha256=px5-QMRr7fXum5SrJowUIKRg-rby3YSZjPA92-voCxA,731320
scipy/linalg/cython_lapack.pyx,sha256=fDKWgTGV8swZzyxFvtHkeaQaVWzrze0t5lHYGu0GKgk,707005
scipy/linalg/decomp_lu.py,sha256=FPo9NHe9wg1FhCaoVV1_4mdfNj0S4plT4dHr4vMl1U8,593
scipy/linalg/cython_blas.pxd,sha256=DCPBxNWP-BvdT_REj6_a4TjUrNaf6sCq_XoxU3pEbfc,15592
scipy/linalg/_blas_subroutines.h,sha256=B2Y9A7ejwt3PHSLLS5mX2VhF6jwP3XgKMmt53O3cowg,18174
scipy/linalg/decomp_svd.py,sha256=HrJqbmgde7d7EWxCsa9XkS9QuWgPYMFOHiF4NcAL_Qg,631
scipy/linalg/_procrustes.py,sha256=uqPSMCxvqdbYMv3YEGUvwhnZnyIaApknfJcNAfyiTBQ,3520
scipy/linalg/_matfuncs_sqrtm.py,sha256=-qdBb42d2HvSkyVi-90N4Ai5vzwkqwGL00duzi_V1jM,6268
scipy/linalg/__init__.pxd,sha256=0MlO-o_Kr8gg--_ipXEHFGtB8pZdHX8VX4wLYe_UzPg,53
scipy/linalg/matfuncs.py,sha256=vYw39D2LukCRCFJpx0qx8tgHlRZEDZI2wZfZwhh-Ubo,744
scipy/linalg/_matfuncs_sqrtm_triu.cpython-313-darwin.so,sha256=sSglucBuPfp69xEDhRhHUy2-twzXA-yM0nmoE5WPA_o,209960
scipy/linalg/_decomp_qr.py,sha256=PbkwukMtzEH94uVjO9IEqSg4xmi0PV-UHXg9iM15rRE,15388
scipy/linalg/_solve_toeplitz.cpython-313-darwin.so,sha256=UW2GEEol6JozPeFdOHWr1LsbMLyMc9Ieruiqeku-P6I,227544
scipy/linalg/_decomp_svd.py,sha256=Epk7P6mmLLmYDiRETZAb3O2v3wKfbOjmGseWkAUlRPM,16809
scipy/linalg/_decomp.py,sha256=D3WgtUo43h4Cjb-9vLepEVs_7BSXX1wYLWBtdmhRO_M,61881
scipy/linalg/__init__.py,sha256=UOFZX4GCusrQjcaPB6NNNerhsVDe707BvlfE7XB8KzU,7517
scipy/linalg/_linalg_pythran.cpython-313-darwin.so,sha256=aemyEjMV31L5Lhru6VZ42-2MM0JL0j3CgHUgZP-cuUw,144104
scipy/linalg/_matfuncs.py,sha256=oOrSsB4tKtgGwFV2YJSUf0I3rTl9ZqCpF2WgHleDn70,25177
scipy/linalg/_decomp_lu.py,sha256=bCwCzMX_StEoLg1vScxglenyCzqMw3-BGJQmBcNEqNM,12941
scipy/linalg/_decomp_schur.py,sha256=OOzr2woTgWHBrJETNRCrzdviLTjiSDcxBgM6gTVkZMY,12059
scipy/linalg/_solvers.py,sha256=zwhrz0DbJ6wf9fY7B0pZMvMoC-cHo1VYXd3DyHk7pTg,28800
scipy/linalg/_cythonized_array_utils.pxd,sha256=OlWTbJt3gmdrfRFyx_Vz7GTmDTjr8dids5HA4TfC6R0,890
scipy/linalg/basic.py,sha256=AuNvDlH8mnAJScycj4mV-Iq1M0bXxidpY4Vud_lRJlM,753
scipy/linalg/_decomp_polar.py,sha256=arzJ40FP1-TFsRvXPCP1qdNTsT60lkBcKBHfhB2JxxY,3578
scipy/linalg/decomp.py,sha256=w9HTI1OxXpX_rL72qcmykc5dUWal7lTlAU8k-9Eq7Dg,708
scipy/linalg/interpolative.py,sha256=8kCZv1z3UtzBuPvompAUUjHToLta4ffvOjVVLSaRLeQ,32757
scipy/linalg/_decomp_lu_cython.pyi,sha256=EASCkhrbJcBHo4zMYCUl1qRJDvPrvCqxd1TfqMWEd_U,291
scipy/linalg/blas.py,sha256=-D-IH0bah8h2SmrdVA4xPfIqmKiPTkVC14GJ3czelLA,11685
scipy/linalg/cython_lapack.cpython-313-darwin.so,sha256=o2yFu-_duyJkSPZspnHHqkh1dPigKQSM_7kTNVFpW7s,703232
scipy/linalg/special_matrices.py,sha256=OXkkDj-ypZHiC17RUerraAzO8dC9aDuVujzb3Ft3GDY,757
scipy/linalg/_decomp_lu_cython.cpython-313-darwin.so,sha256=hF22o9rlTnw0BbtDEyZySW4dSLW1C30E8Yeq1vFm3Vg,208136
scipy/linalg/_decomp_ldl.py,sha256=HYzVUNZgEyuC2ZoFOGneas8ZkhhOFzUGcapL3Pos_cE,12535
scipy/linalg/_decomp_cossin.py,sha256=rf2DFhaDmpXnWr1YpL3s8-hTOlR42HfSyWN7OoWzrec,8977
scipy/linalg/_lapack_subroutines.h,sha256=oTfgwlGeOWm1A9EL1ybTxS5cURQbHXnI3wQMuUr3fH0,239326
scipy/linalg/_cythonized_array_utils.cpython-313-darwin.so,sha256=t2mOGlonliKO-nzDZ2NZ27FQebDd5zPGEA8hItCtIEQ,472400
scipy/linalg/decomp_schur.py,sha256=vkVK3y-055523Q__ptxVNatDebPBE1HD-DFBe7kEh3w,602
scipy/linalg/cython_blas.pyx,sha256=OgjBmmw5YP51aWtU-1pFgWWH29XWcnFWwcZP3_1sELk,65288
scipy/linalg/_decomp_qz.py,sha256=uH93in1ikPR-Wgi1g49EPm2XXuhKOWBzPUJEahCotx8,16330
scipy/linalg/_expm_frechet.py,sha256=Yc6J9HICUULvXcYBUaCyoOPFhXwjkIFi7TdrcNeVEmo,12326
scipy/linalg/_matfuncs_expm.cpython-313-darwin.so,sha256=j05AQI_jGczcdy0KLARcfmg1kP6Ira-lqyoJvRfPGh0,105288
scipy/linalg/cython_lapack.pxd,sha256=Ld5hPwcYxpOPahFNsfNomsp0_DY8BfG-W8TmZxh-iYM,204556
scipy/linalg/cython_blas.cpython-313-darwin.so,sha256=MDmGx1A4y4pXHHIUr94SIKumhcQXTTAQKuPVwkej3AQ,261360
scipy/linalg/_matfuncs_expm.pyi,sha256=wZAZfVtEbB78ljXgQoiL0I4yaPhmHOqIpGBYGQPvS6k,178
scipy/linalg/_cythonized_array_utils.pyi,sha256=HZWXvJdpXGcydTEjkaL_kXIcxpcMqBBfFz7ZhscsRNo,340
scipy/linalg/_basic.py,sha256=5LXUCE49zLfVNzU1V-0HrsHWkFsNe16wzZ9cu2LubW0,76085
scipy/linalg/_misc.py,sha256=udhvxGfEHxhS3ecQBuwQ65W9ezVQIaVBw8JOmfqH_oE,6301
scipy/linalg/lapack.py,sha256=0bytum8c_A1Xdt5NH5dcol7GjFtrkjuAnH_cnLWr07g,15805
scipy/linalg/_special_matrices.py,sha256=ZmOTcoJfbsR3baZgHWQ80extNyYJeSo8Tx81nUmzkyc,40697
scipy/linalg/_fblas.cpython-313-darwin.so,sha256=pBtAIzj22EK2N76OBsrJQa1mKHPaiPyPVti8GTTVue4,537104
scipy/linalg/_flapack.cpython-313-darwin.so,sha256=8sXt-wc9J4M2JBTS7jCaHIRRd_rfZoAq3G-CIe-piIs,1784736
scipy/linalg/tests/test_matfuncs.py,sha256=yXWlWUswLo_pDbKmhY8OkBSPfCrRXlU2om2QbwTAHIU,41997
scipy/linalg/tests/test_decomp_ldl.py,sha256=f6rUwqOxRNr0C3lM0zX0PjAj4yLi3T_bmKdAUGpW2xg,4971
scipy/linalg/tests/test_basic.py,sha256=ykpAEKYmPCxF0mrUQUHzJIahmXzzFqrU4thGEVRKdqE,78883
scipy/linalg/tests/test_decomp.py,sha256=IlzcrZlmRNPcdf8yF0Dixoj3W7OB-RaycKZYq4S16Lc,118686
scipy/linalg/tests/test_cython_lapack.py,sha256=McSFDUU4kgCavU1u3-uqBGlzUZiLGxM5qPfBFgPTqdE,796
scipy/linalg/tests/test_fblas.py,sha256=Ykb7LKjbxPXAdJD-IkXMAsbUmXMAkku2FQCr-jlDTUE,18687
scipy/linalg/tests/test_matmul_toeplitz.py,sha256=73Qe51lCXEWZGpxk8GYv0owDSlN0IpnLJPlI0nsCdhY,4088
scipy/linalg/tests/test_decomp_cossin.py,sha256=QCIIlzrhJR9K_4WLniwR7JuaYyA3_3jPtScBJx4NU3c,11982
scipy/linalg/tests/test_extending.py,sha256=eirY2TQ2IwWje-5hW_kqvS0SnA2xEzLeG5sE0P3zuvI,1751
scipy/linalg/tests/test_cythonized_array_utils.py,sha256=vZh0gT7cN7m5H5xox5ClQT_GxoBbadRtYDBNKBDnhZQ,4172
scipy/linalg/tests/test_solvers.py,sha256=jIJ1YjC5epuQACS2h7GZZUuIbt89KPM8tnUlXTsPyjU,33951
scipy/linalg/tests/test_cython_blas.py,sha256=0Y2w1Btw6iatfodZE7z0lisJJLVCr70DAW-62he_sz4,4087
scipy/linalg/tests/test_sketches.py,sha256=FLqc8wn9esU8LbSsWS7_OC0sZ-BcGPROqPurBM8BZXc,3954
scipy/linalg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/linalg/tests/test_lapack.py,sha256=M5Q_VvWz-7LANoqK7l8yyslf18jNouG2gaX7QZVtaJ0,134781
scipy/linalg/tests/test_special_matrices.py,sha256=CyWH9bbVGogK-ECymnhyxogMDEMeOC2BN9A2XDYg-eE,25074
scipy/linalg/tests/test_decomp_update.py,sha256=MCSzhUD-bcCs1Ll5pHJqCdRTgEpimCglZ3lb8bzwZqs,68502
scipy/linalg/tests/test_blas.py,sha256=8w_6r4CBrif9MH69v15Iil5rEcyRDlUhgbbZnC8_Bck,41729
scipy/linalg/tests/test_interpolative.py,sha256=EVmkopJjhzDOs6h6NoSkQ-d7qRZDsys58mt4sp8yOoE,8577
scipy/linalg/tests/test_decomp_lu.py,sha256=spCYelU_CXmHAaKrJM4V5djLKq5MCeX4wN1SBCFkSOo,12629
scipy/linalg/tests/test_decomp_cholesky.py,sha256=5WxQbSxK6134NztaoNu-d4OmudQRfhgeyf2LmyJdx1w,9743
scipy/linalg/tests/test_decomp_polar.py,sha256=fGKl3Skqz6IpHBeFcq6bdqvS8M53rXx2Wh6Kx4f5T3Y,3287
scipy/linalg/tests/test_solve_toeplitz.py,sha256=5dmvPEpOwHAucdoMhT1lCvEMIbMrgpZwj9nUL1WRb2g,5122
scipy/linalg/tests/test_procrustes.py,sha256=zOl2G-PENDtEZGk4AVdqIp_4zUWoHmhGrj2RyuZRPTk,7660
scipy/linalg/tests/_cython_examples/meson.build,sha256=AoGSc8a6hX_ivvj4MgP_stTLu2ant4ALdknPMYQlaZ0,670
scipy/linalg/tests/_cython_examples/extending.pyx,sha256=scunPSonBTtsidhd2hLtg-DPWoFkvzWcXDMYEO9iygo,887
scipy/linalg/tests/data/gendare_20170120_data.npz,sha256=o9-rRR2dXCAkPg7YXNi2yWV2afuaD4O1vhZVhXg9VbU,2164
scipy/linalg/tests/data/carex_19_data.npz,sha256=OOj8ewQd8LI9flyhXq0aBl5kZ2Ee-ahIzH25P4Ct_Yc,34050
scipy/linalg/tests/data/carex_18_data.npz,sha256=Wfg5Rn8nUrffb7bUCUOW7dMqWSm3ZPf_oeZmZDHmysY,161487
scipy/linalg/tests/data/carex_15_data.npz,sha256=E_PhSRqHa79Z1-oQrSnB-bWZaiq5khbzHVv81lkBLB4,34462
scipy/linalg/tests/data/carex_6_data.npz,sha256=GyoHNrVB6_XEubTADW2rKB5zyfuZE8biWBp4Gze2Avk,15878
scipy/linalg/tests/data/carex_20_data.npz,sha256=FOIi00pxGMcoShZ1xv7O7ne4TflRpca6Kl7p_zBU-h0,31231
scipy/optimize/tnc.py,sha256=aEKhka8wryg4mVlbrGFwzTJF_KYB49joMkSxKgh1KnA,560
scipy/optimize/optimize.py,sha256=SivH06ZYrbIwJLTQj3ZShU4FXft7w2y1a2uYE9ILIMo,877
scipy/optimize/_bglu_dense.cpython-313-darwin.so,sha256=uH1LECSS203TNunaktTtpfNApdCzSyQoqk3BEql5qKs,282544
scipy/optimize/_linprog_ip.py,sha256=dEaU1pqYXRxWvH91Zxm4tMQ7813QNhjIB8Yj8Nb3cPY,45784
scipy/optimize/linesearch.py,sha256=w5OhOofynUbz7IzHAGEc6huLKV_rMR5eUq77VcskA9o,535
scipy/optimize/nonlin.py,sha256=uoKIYAdmhwNrC6zFbUIBCNdM1a59nn7hb5jxSOuK3rs,710
scipy/optimize/_linprog_rs.py,sha256=wRVGZxCSpo4ttw4CPpmXozSvM9WRXD179fGiGh8gOQ4,23146
scipy/optimize/_direct.cpython-313-darwin.so,sha256=WxMcJ__HLd3CXAo8-NE-OYO-Vgz29BX1pHrW6tD6Hcw,69296
scipy/optimize/_cobyla.cpython-313-darwin.so,sha256=zkbi6qJKKTmS4FBPJUPODKol6MLqEAn7LCRokkkKybU,124848
scipy/optimize/_root.py,sha256=Zh-WttrslloClCDg7VKhrVbRkDHBRkS4-ijJkI-_twg,28714
scipy/optimize/lbfgsb.py,sha256=XT7kclUTtom8JASPYyAScx-5irlBd9s9yEnZzRwFqu8,601
scipy/optimize/_group_columns.cpython-313-darwin.so,sha256=ymkPPSuAXce-vYnIaOjGjOoAZ_qx_V1RaD5Y-wBJNoE,94568
scipy/optimize/zeros.py,sha256=Sc06-J8JUazdfR36UamHhPndJoPK0FkOzHR-unHWoBw,620
scipy/optimize/_cobyla_py.py,sha256=_HUCEYEEFxNBniaw56eZqmjsrwCOMbOTdFaYUv5UqUI,10867
scipy/optimize/_shgo.py,sha256=y5ET23yh6LS0yltoVaeM3CH7gundIfAfUhOEKq09ksw,62399
scipy/optimize/_optimize.py,sha256=AzljBSSf7wAO_G9W8pkg-o3IdlHzMdp5JulhMGcoORM,147685
scipy/optimize/_linprog_simplex.py,sha256=9_nxcVl-ofHN9p_dDyC1C6jHlPttSfO9kp8WF1ST4JM,24748
scipy/optimize/_minpack.cpython-313-darwin.so,sha256=3RYg4v5JbzQR0tE6n4fDvFuhxXoqLHt08ldhZ0qKMUo,103344
scipy/optimize/_milp.py,sha256=KYJlJ0NulFZoO6d1yactJmhryLuPzmiRS8GIxqWXxbU,15227
scipy/optimize/__init__.pxd,sha256=kFYBK9tveJXql1KXuOkKGvj4Fu67GmuyRP5kMVkMbyk,39
scipy/optimize/_linprog_doc.py,sha256=AeDv_zu0iU_oV0vxSrdzzY5GkKzOVx-5nmBgFB_UXhA,61942
scipy/optimize/slsqp.py,sha256=K7nXxF99sjaI3_eoOm9w0VnrbaQXgnHlvvgs8lNa0zA,582
scipy/optimize/_trustregion_ncg.py,sha256=y7b7QjFBfnB1wDtbwnvKD9DYpz7y7NqVrJ9RhNPcipw,4580
scipy/optimize/_minimize.py,sha256=MGd3sP6LNwpElRiW85iHxBEinhaohly0gfOLxhtUs7s,50135
scipy/optimize/_zeros.cpython-313-darwin.so,sha256=aGpeZmRJtk2pj5jv6GhZt8jJNFvNYcUbVQ2K0gdbbHY,51280
scipy/optimize/_isotonic.py,sha256=WY-9jtT5VVafVALYIp6lJPQnBfYVNDP9oJpg-kErYYI,6077
scipy/optimize/minpack.py,sha256=2S9tkmBI670qqeDN7k_1-ZLYsFZV1yXaDMkrCvMETiQ,664
scipy/optimize/_lbfgsb_py.py,sha256=KgLYyR-UeQg8chw-ttdarm5blMuop5lY4KqI_Hqk-2c,21047
scipy/optimize/_zeros_py.py,sha256=pN0GMI_qHtor8BnY73B49bDZiiSYAxY1EtsQ3Kf0BJ0,52066
scipy/optimize/elementwise.py,sha256=8eEQW_PeNkr49YBTROr5xWDLgeJd7rxtdQk3tVuEECQ,1190
scipy/optimize/_slsqp.cpython-313-darwin.so,sha256=Ngcnk8x58dVSSDngf3MYsqIwT_PeUyU10cjNg8PWOwY,123888
scipy/optimize/_basinhopping.py,sha256=Ug6gQH56vjrs-6RwGZKyCgVzjkT9rgqOPH-sJSaWtmM,29778
scipy/optimize/_linprog.py,sha256=TGl9k9Ioh-hgHYgtndN5BNcU4vqfpZm8whRK2f4ehQQ,30262
scipy/optimize/_slsqp_py.py,sha256=8KNFRiJlhinsqSMIp3-lzjrrw4lcrV7CADf1N6k87LA,19066
scipy/optimize/_hessian_update_strategy.py,sha256=xmtREKGlLgVvlBynjb5eCnPbsH-xbPcprS-ZoziG80M,18423
scipy/optimize/__init__.py,sha256=7ZzePqFF1X1377f_s3dpVdeg51I3YwManuh8Pl4M1mE,13279
scipy/optimize/minpack2.py,sha256=IPIduBcu0LRo75GJ9SiMa_GjfdKCOYzsWUs61_d1HR8,514
scipy/optimize/cobyla.py,sha256=k2io8SM0vahYT5Zu4nS4yfa05_gyH0y-jVVxdWkC4dU,557
scipy/optimize/_elementwise.py,sha256=2CYFgK7uYw0St-T5M-GAhh8zgB3yU0mHmjS1Q6YYrNA,33136
scipy/optimize/_linesearch.py,sha256=sZ45z0K3l6LLURdAfzO5CI5DctDlXqD92PCaz9mKzYE,27215
scipy/optimize/_tstutils.py,sha256=BBaThpZNuwIQBqtVMOEB4bUHk3QdG2NpuLJBum8P6ak,34047
scipy/optimize/_chandrupatla.py,sha256=cmgXWc33PxEUUVn2Bh5Go4XPx_K7Hzihb2DyUAn8C80,24639
scipy/optimize/_nnls.py,sha256=td0FOAvUICeUTGrXqFmdV6UXGi_Cy0PrG8hQviDsqe4,3233
scipy/optimize/_spectral.py,sha256=cgBoHOh5FcTqQ0LD5rOx4K7ECc7sbnODvcrn15_QeTI,8132
scipy/optimize/_differentiable_functions.py,sha256=aYwpOvlHfQ7j-BO15VcL1v5XLR36tr_OPmf1eCWLuHY,24922
scipy/optimize/_trustregion_krylov.py,sha256=KGdudJsoXXROXAc82aZ8ACojD3rimvyx5PYitbo4UzQ,3030
scipy/optimize/_trustregion.py,sha256=z3yOE3-PGbIviDYTqpPQqa5wQhTMqc-LvssbY9Eou0A,10801
scipy/optimize/_lbfgsb.cpython-313-darwin.so,sha256=21E8emcg0E7gED3xDg3m6IoHU-DSYqtzkBxNJ2wEKO4,68576
scipy/optimize/cython_optimize.pxd,sha256=ecYJEpT0CXN-2vtaZfGCChD-oiIaJyRDIsTHE8eUG5M,442
scipy/optimize/_pava_pybind.cpython-313-darwin.so,sha256=oc8oP8_EsK7vFS2kKA_OMJZqkINoxDQ7Mw8Rxjxj9uA,180080
scipy/optimize/moduleTNC.py,sha256=qTEQ4IWtv_LT6fH3-iYmYNwrtrjG1gS4KFbZ73iDcd0,507
scipy/optimize/_minpack_py.py,sha256=sjx90i41TQ9CzXtr2LVkxP-woc2L_8v7YHVXidSpRK0,45028
scipy/optimize/_linprog_util.py,sha256=W85k22zMLJMAEZs_UHMqR5OxHtykyKoyHQBUCa3YAw0,62799
scipy/optimize/_moduleTNC.cpython-313-darwin.so,sha256=RxyWZ9BQIpD1zWDEIa4d-vxNP5FWx6RSCJ5KVch2Yt4,147648
scipy/optimize/_linprog_highs.py,sha256=yN9w71Hs6qFYBNg21L6gz61-szlmLPUafblZEryyzy0,17144
scipy/optimize/_lsap.cpython-313-darwin.so,sha256=cckR_-cOmBAzCW2rX3dJYRSen6bd0L1k_1DUQqs9CkU,70696
scipy/optimize/_direct_py.py,sha256=-tEx51_9jg63zmDcSmmqeMtTlxXpci8fSh9TR_dFD4M,11849
scipy/optimize/_dual_annealing.py,sha256=Zr5O-Juk2lslIlneQ4J9sgmDlPKh6sRZ9ytZZ9i-x7U,31121
scipy/optimize/_cython_nnls.cpython-313-darwin.so,sha256=XPEO_he9pIScnLf0uaOP941hoJ8iNzyiGtX5Jp2pcio,114064
scipy/optimize/_root_scalar.py,sha256=PIVT37WbcUwytG0WsU_t_pkUiluqZcJUan61ErBo_7I,20391
scipy/optimize/_trustregion_dogleg.py,sha256=HS783IZYHE-EEuF82c4rkFp9u3MNKUdCeynZ6ap8y8s,4389
scipy/optimize/_cobyqa_py.py,sha256=_zejgs3XKkieGiMlRVn1x12cyWoulaPP2SpvxA4zK3k,2971
scipy/optimize/_tnc.py,sha256=hmnQHaS5FLoaLzPHLcIVU2NPeO_-EQuJCc1Z8RLqDVs,17009
scipy/optimize/_trustregion_exact.py,sha256=zaMQc5wUhZSnpxyXWwcqIh0O9bctOU4R-isaeblvSNc,15558
scipy/optimize/_constraints.py,sha256=K37Le2W-pA7fsR39wXiC3L60QZGFN_-EUhtmGie-qn4,22895
scipy/optimize/_differentialevolution.py,sha256=UrTsxsTC1ddNoBsZ2tnNI0Lpz4HUC0QlmcaA1wCiQPc,86506
scipy/optimize/_dcsrch.py,sha256=D5I9G4oH5kFD2Rrb61gppXFMwwz6JiQBYPvW3vbR5Gs,25235
scipy/optimize/_nonlin.py,sha256=BtDRlEwSlvOhxo04mXQHpzytoV-FI_K5yVs0RAX8eBI,50177
scipy/optimize/_bracket.py,sha256=tVevTxrwC9YyCgDKDCNrsxZyY6Hj8yM2F44kqawMCgs,31308
scipy/optimize/_remove_redundancy.py,sha256=JqaQo5XclDpilSzc1BFv4Elxr8CXlFlgV45ypUwALyc,18769
scipy/optimize/_qap.py,sha256=6bIzIiLwD4V2MCJrqQBOJ2h7uycy0qx01mkl-CR1U3I,29390
scipy/optimize/_numdiff.py,sha256=CpeUGKWHTsAk-JnvtbDBjpXvlI8pch1oXIPj40CNY2c,28931
scipy/optimize/_trlib/__init__.py,sha256=cNGWE1VffijqhPtSaqwagtBJvjJK-XrJ6K80RURLd48,524
scipy/optimize/_trlib/_trlib.cpython-313-darwin.so,sha256=DJYL8v6Uyvvo8w43XX8L9qvfSAwAephGadWZniU4P-4,278848
scipy/optimize/tests/test_hessian_update_strategy.py,sha256=EiL5ImqkGFmUTjgZjv0FGpGBjTzWXqT3w6eCrzQtPmo,14337
scipy/optimize/tests/test_chandrupatla.py,sha256=zX1XDkfp11bB_krw0mKGb0_XgXjhNdIltpiFhuGKmMc,39020
scipy/optimize/tests/test_lbfgsb_setulb.py,sha256=6Aqn26aKUJp75unFqCAzesLq_tWPsQpp2rCftauSOS8,3582
scipy/optimize/tests/test__remove_redundancy.py,sha256=gwakPkJo8Y8aRL4son1bp8USfwc9uMrLLnZFrDmfvxY,6799
scipy/optimize/tests/test__linprog_clean_inputs.py,sha256=9HFrqlU1OHGTHCgy_R9w2rJ5A5xlu_3QpGbnzQezqXM,11678
scipy/optimize/tests/test_constraint_conversion.py,sha256=7uRZeOxVD6KFbyVi6h-PSts3BxBPFiFZPVczhiVd5b4,12563
scipy/optimize/tests/test_cobyqa.py,sha256=5sHRoBc4ZVfjZZAYMGObwSAtWq2A53L9KSwHuUUhQLk,8143
scipy/optimize/tests/test__spectral.py,sha256=xh-4SMIAWkx_ND2nt7rGACy3ckfw_votfyfxMpQ8m2I,6664
scipy/optimize/tests/test_direct.py,sha256=_R4_VkYkIJcS7X9a7n9rxwnZClK5i9nXSiYYkX0aRiA,13267
scipy/optimize/tests/test_extending.py,sha256=r9Phn1PUn0U3U6QJeMiPreKG6jKmnWFqwpf1Al7w7K0,1104
scipy/optimize/tests/test_tnc.py,sha256=ahSwu8F1tUcPV09l1MsbacUXXi1avQHzQNniYhZRf4s,12700
scipy/optimize/tests/test_optimize.py,sha256=RdQOf5np2uLgZ5WnN-Ay5YOOtWfU_Mdcptur86xH3pU,127471
scipy/optimize/tests/test__basinhopping.py,sha256=t2JHeg0qy4gUbKuPog9BcwgYyvwcPoh0zbrThoasWnI,19210
scipy/optimize/tests/test_linprog.py,sha256=8yqKv4Gx7mwlnLGOhNwpDwCMuhpQurJ6CA1jONNeeX8,102678
scipy/optimize/tests/test_nnls.py,sha256=jr0xf8WA-tis91BC0kAKmKl3RiBFTr4deWat4d_iwAI,25763
scipy/optimize/tests/test__dual_annealing.py,sha256=8qzPbCQwqmNRJ2GYk1X02qNvmF3TAgJxzUG_x0c07o4,16640
scipy/optimize/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/tests/test_trustregion_exact.py,sha256=pPY_GRZZ0dwXqUboObatYMpRuwVSwRScCfuu4WkuSbw,12933
scipy/optimize/tests/test_lbfgsb_hessinv.py,sha256=rpJbiCUfgJrjp-xVe4JiXjVNe6-l8-s8uPqzKROgmJQ,1137
scipy/optimize/tests/test__differential_evolution.py,sha256=yUs5lEXkvpv-s-r7EDBNaPorE56xKcgBwKgXtbEoASQ,69522
scipy/optimize/tests/test_least_squares.py,sha256=MG9-lpqEQHJBH9eoRgRjWFCp2gwGRdSfRTirV53Q3cY,34021
scipy/optimize/tests/test_lsq_linear.py,sha256=5bVPsp26HdqQ9kF4CdkQEyrm8yjjLX1LB22nV83Muhk,10959
scipy/optimize/tests/test_isotonic_regression.py,sha256=aJakW5zYcILN3wa--CYFBoZ3MB6n5Rzwd4WfNs_SFQk,7113
scipy/optimize/tests/test_minpack.py,sha256=sOCIbIGKursdT4EBc5T6U7LT7JevCsIIWK39PWOOAb8,44841
scipy/optimize/tests/test_constraints.py,sha256=03SN10ubXpgrNq9Z4DEpPSC6hTXznW-YUF-nxdaxSQ4,9408
scipy/optimize/tests/test_milp.py,sha256=V4KeW9Z3CfCvCk_NT88yqvw9E_t2r-aIq-yJFwVIaWY,18302
scipy/optimize/tests/test_nonlin.py,sha256=N5iZpgXu0Q7aNkznOtEGC28POBVJKniiGMgMDA2M_JM,18559
scipy/optimize/tests/test__root.py,sha256=yBSibeODBJwOqjTJHWXP9qWqh_D9XBnMjn5hFuTVQpo,4230
scipy/optimize/tests/test_lsq_common.py,sha256=alCLPPQB4mrxLIAo_rn7eg9xrCEH7DerNBozSimOQRA,9500
scipy/optimize/tests/test_minimize_constrained.py,sha256=ulswdUxITmCsav69ghAI3SysmD1WnFYja3JFHVk_bYk,27942
scipy/optimize/tests/test_trustregion_krylov.py,sha256=otFMoHYcJZzPdyv7UKOgerehGJXpOB8YWP0-lYHYhUk,6616
scipy/optimize/tests/test_zeros.py,sha256=jLxGJNc7N8qPbTpRtf23ZeRrg6lzlW53slD8yA6al9s,36760
scipy/optimize/tests/test_cython_optimize.py,sha256=n-HccBWoUmmBWq_OsNrAVnt4QrdssIYm4PWG29Ocias,2638
scipy/optimize/tests/test_quadratic_assignment.py,sha256=4BKOjpEPgSi0YATody23JUjzZ749rh-F7sMWlpuvy4g,17598
scipy/optimize/tests/test__numdiff.py,sha256=QEkhiCcGHO2CJLaJHXcq4ILDedtIpleEs3AQdQ-ME5Y,32359
scipy/optimize/tests/test__shgo.py,sha256=Bi_0KCdDhnWUbh9KWwGoLkV4BTJ6Fh0FT8mQU41IUa8,39804
scipy/optimize/tests/test_trustregion.py,sha256=y49k3H03wdf21FFrUBJpJP7-sqvbxRdvk63cMHkKO3Y,4669
scipy/optimize/tests/test_bracket.py,sha256=V-f_GEBCqwNOjFoqKcTg5OhglGvKHMqDqZjthwR5VwM,37043
scipy/optimize/tests/test_linesearch.py,sha256=xmK2zvgIbLMOWkb2B1ALBWiPHQyGGxzDG0MXaHjNlqA,11400
scipy/optimize/tests/test_slsqp.py,sha256=GZn35XMVZQ1ouzdgKseNRI9ruWP4vr1HOcLGK3a8g4E,23518
scipy/optimize/tests/test_linear_assignment.py,sha256=84d4YHCf9RzjYDKUujQe2GbudkP8dtlSpZtMBwCf_Oc,4085
scipy/optimize/tests/test_differentiable_functions.py,sha256=Dh3JD1bbmhEgAA1w7tfQFV7HpkBahHHQYsMZII58DFg,28489
scipy/optimize/tests/test_cobyla.py,sha256=UXlHcEYwaJNWVtAr60t2UpGA9TdpPyTud_tx13LmIuI,5272
scipy/optimize/tests/test_regression.py,sha256=CSg8X-hq6-6jW8vki6aVfEFYRUGTWOg58silM1XNXbU,1077
scipy/optimize/tests/_cython_examples/meson.build,sha256=GCeweHtWXjvk73tZN3HqsMTw7F1St0JuIhGyxmEiPv0,703
scipy/optimize/tests/_cython_examples/extending.pyx,sha256=5TCYF9hvIYu8S9Y7PIql-xdJfcn_LI50yDrf4uh7i2M,1314
scipy/optimize/_shgo_lib/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/_shgo_lib/_vertex.py,sha256=I2TAqEEdTK66Km6UIkrDm2-tKpeJUuFX7DAfTk3XvUg,13996
scipy/optimize/_shgo_lib/_complex.py,sha256=Ivs6HoVpIaVrS1wMiJC5FhV3N8VZKvoVSkcZ8YA191s,50224
scipy/optimize/_highspy/_highs_wrapper.py,sha256=wVqUOgmFv3FthLk3GdCy9XLmmDc2VasCWGFLSyq2cwM,11294
scipy/optimize/_highspy/_core.cpython-313-darwin.so,sha256=D633vgtQWTqV3n7eYFq_RdJ0EIOmecKpbIw_Vze2nWU,4603256
scipy/optimize/_highspy/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/_highspy/_highs_options.cpython-313-darwin.so,sha256=HAoIPfPnOxESqZFqG297jxOVwl7N4G4q1Ha6AVCLrBc,341624
scipy/optimize/_lsq/least_squares.py,sha256=M_bznCB4ueIt9hklMVu4mCXskIKkZe1AVBL5biaSvTY,39302
scipy/optimize/_lsq/dogbox.py,sha256=97htRlr-Yt-********************************,11682
scipy/optimize/_lsq/givens_elimination.cpython-313-darwin.so,sha256=-y8b3jUwlzo6AHNgf1qZxi5L8TfVSyN3fst_Kid243A,190968
scipy/optimize/_lsq/__init__.py,sha256=Yk4FSVEqe1h-qPqVX7XSkQNBYDtZO2veTmMAebCxhIQ,172
scipy/optimize/_lsq/trf_linear.py,sha256=jIs7WviOu_8Kpb7sTln8W7YLgkcndv0eGIP15g_mC4g,7642
scipy/optimize/_lsq/common.py,sha256=kNsAyIAPFPTEJqQCKUwR8NEbYWtgINDoF76SBg-rU6Y,20476
scipy/optimize/_lsq/lsq_linear.py,sha256=JWhGY2GJmeQoi7ZU0dg-TFSRIGvdNAgHhIaPK9GNOUA,15037
scipy/optimize/_lsq/bvls.py,sha256=7u5B8LfUbv3ZRZ8DAZKuDTSNRfDEBmTsn25VZtMMsKk,5195
scipy/optimize/_lsq/trf.py,sha256=ElVHnB2Un3eaQ4jJ8KHHp-hwXfYHMypnSthfRO33P90,19477
scipy/optimize/_trustregion_constr/equality_constrained_sqp.py,sha256=eJc1Y25WhSLC6OGNJSFw0uA0c6LSUgfTQzmyXsXqVog,9154
scipy/optimize/_trustregion_constr/__init__.py,sha256=c8J2wYGQZr9WpLIT4zE4MUgEj4YNbHEWYYYsFmxAeXI,180
scipy/optimize/_trustregion_constr/tr_interior_point.py,sha256=rRly3wy-O-MQ0dF2lc7b1IwTYWYXE_k87MzYnAW7EJw,14400
scipy/optimize/_trustregion_constr/qp_subproblem.py,sha256=EtAhRcEtSnGsEeEZ2HGEzm-7r0pnXMCgl9NemKWvdzg,22592
scipy/optimize/_trustregion_constr/minimize_trustregion_constr.py,sha256=WpVDoMk7rFHJI2KSG2YWiBm6bli180KvLneK9TVfz9Y,26145
scipy/optimize/_trustregion_constr/projections.py,sha256=EO0uHULrNw8pm99vY-gd3pOFQEqrqk_13lVde9iUjTA,13169
scipy/optimize/_trustregion_constr/report.py,sha256=_L-HrO5C1lzvKvaijgkOYD210dvM4PkrhBSEQrMhVlw,1782
scipy/optimize/_trustregion_constr/canonical_constraint.py,sha256=lWdsJ7WNTDm17jD-Omf5lflSMfcvdZWpReCND2CyjI0,12549
scipy/optimize/_trustregion_constr/tests/test_canonical_constraint.py,sha256=zVPxZDa0WkG_tw9Fm_eo_JzsQ8rQrUJyQicq4J12Nd4,9869
scipy/optimize/_trustregion_constr/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/_trustregion_constr/tests/test_nested_minimize.py,sha256=tgBVQe97RwVu_GJACARyg0s9zHiFGVHSPNrXLCjlX7w,1216
scipy/optimize/_trustregion_constr/tests/test_report.py,sha256=hyRnUGBhDhKHR5SKD66ZME4zzCIViIh3_-700p0afXY,1104
scipy/optimize/_trustregion_constr/tests/test_projections.py,sha256=-UrTi0-lWm4hANoytCmyImSJUH9Ed4x3apHDyRdJg5o,8834
scipy/optimize/_trustregion_constr/tests/test_qp_subproblem.py,sha256=bU_4_VHpQZpCnC733G-rakx3Mxdwt4QndCM31mUH4vA,27719
scipy/optimize/cython_optimize/c_zeros.pxd,sha256=6Gc0l1q-1nlCO9uKrYeXFiHsbimRZzU3t6EoTa8MVvA,1118
scipy/optimize/cython_optimize/_zeros.pxd,sha256=anyu-MgWhq24f1bywI4TlohvJjOnpNpkCtSzpKBJSSo,1239
scipy/optimize/cython_optimize/_zeros.cpython-313-darwin.so,sha256=f27yrAunY-DBRo_KM8Z1fFqLcv3EiJncA-O8UmRD-ls,114400
scipy/optimize/cython_optimize/__init__.py,sha256=eehEQNmLGy3e_XjNh6t5vQIC9l_OREeE4tYRRaFZdNs,4887
scipy/integrate/_quadpack_py.py,sha256=jOeoUlpqTEOh7Qw7RJxwxt5ojsW9iVsF0CaQ_kk0esE,53250
scipy/integrate/_ode.py,sha256=Wm6XtYfe11GZWpnTA71N02ib-niAg2ytyens3YPB2Co,48299
scipy/integrate/_quadrature.py,sha256=6u3t4hUh4_3CtdHmaXAtKxB2-IBVPNO37CeEjZyS7rM,47907
scipy/integrate/_odepack.cpython-313-darwin.so,sha256=7IZFzXhJZeZ1JNl6Jpal3zVRPXGM9hbrrY3Vl21vTJY,122528
scipy/integrate/_odepack_py.py,sha256=DhHLB7rx0p6TrQQzQQlwzqcb8oMuFRDra0nIFryb0M8,11231
scipy/integrate/_lsoda.cpython-313-darwin.so,sha256=le0I6QeoAGnhEKALAcyFn1dnpkRLMei72goypuyieQk,126896
scipy/integrate/__init__.py,sha256=CmPLfkF66jXhHsKyQPOsvFEc9nxicRYwl6WDAa7cfJk,4373
scipy/integrate/quadpack.py,sha256=vQNE5jQ-dFpH26er1i8LJSkylFVbeSgVGLwSRQawfYg,604
scipy/integrate/_vode.cpython-313-darwin.so,sha256=mfoQCS8-9Z_ujh4wGMUU6AGQzC8DsyOph-ueKUjpa9s,195248
scipy/integrate/vode.py,sha256=DPRqm2oBQx6KKi5tl9dDVpXEdAO--W0WpRQEyLeQpf4,424
scipy/integrate/_quadpack.cpython-313-darwin.so,sha256=_O9lh1j_9gX15E62WSAbY6qtY-BBVU8ocYf9Dl1S_Kk,104176
scipy/integrate/_quad_vec.py,sha256=VKdZEaWLDNW0-2S3tcGKv386QIcUlwb-vpxPk0_NwGU,22024
scipy/integrate/_dop.cpython-313-darwin.so,sha256=VoCKN2cnSM9UJn1uQXtbf7j1_vG7Ru4q_J83gXq7kfQ,142080
scipy/integrate/_bvp.py,sha256=0EazRKECaOErYe_MAAbmgRrbkdOgSXpwkQfwPLxP30I,40897
scipy/integrate/lsoda.py,sha256=hUg4-tJcW3MjhLjLBsD88kzP7qGp_zLGw1AH2ZClHmw,436
scipy/integrate/_test_odeint_banded.cpython-313-darwin.so,sha256=FDNRUvvlbb4HynjnKSffU__161C19GzzOrox6b5gVEk,126480
scipy/integrate/_test_multivariate.cpython-313-darwin.so,sha256=UrD0XwRjTWvZRYVoJfu4v0lj8aFjy4MJWaWDyuR3WbQ,50664
scipy/integrate/dop.py,sha256=Kx5Ed_Te81X09bvGmBUq3-_kQNdTIsOdO7ykjEpEG9c,422
scipy/integrate/_cubature.py,sha256=DI7iFsEgT4LpuPzXKReXqCWCwhXlsMWvhBiH_tkAKTY,25671
scipy/integrate/_lebedev.py,sha256=Tj3I_tnQ3_mfARK_scDsd9aM5dLe9To-GeaCda5OMKw,262024
scipy/integrate/odepack.py,sha256=G5KiKninKFyYgF756_LtDGB68BGk7IwPidUOywFpLQo,545
scipy/integrate/_tanhsinh.py,sha256=QMNW0HaxhR3gP_LqxYOZBsypDDpuCjuyFPv7SJaMj9g,61340
scipy/integrate/tests/test_odeint_jac.py,sha256=enXGyQQ4m-9kMPDaWvipIt3buYZ5jNjaxITP8GoS86s,1816
scipy/integrate/tests/test_quadpack.py,sha256=8EM7IsCLJxswnWAd8S5xyvWX9dWjudycdvDDq1ci7v4,28066
scipy/integrate/tests/test_bvp.py,sha256=tNSp-4YyIQNyLVykDU77i0-4zzkY0sEwVVaT2uoOvz4,20223
scipy/integrate/tests/test__quad_vec.py,sha256=jkVVrf-7sF_kC3VUIfgBY2LuCeNtFff5G7o7bN3Jedk,6516
scipy/integrate/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/integrate/tests/test_tanhsinh.py,sha256=7eExO_tYFhDVNMdOLplSO9mx6B1PdB4vydDqFNmAWG0,44800
scipy/integrate/tests/test_cubature.py,sha256=_qdTrc718vyv6pCh-nG6X4dcSWffJZsKZ7O9aPBrObA,37018
scipy/integrate/tests/test_quadrature.py,sha256=B4DYgR-tbtWzJKsw05VaJ9aknXpO-N9oZ5--hsE6cyw,28248
scipy/integrate/tests/test_integrate.py,sha256=KiyXeJ7ThQUpL8_XQKfOTZ8i_LBVwgC7ykzF6Yg574I,24611
scipy/integrate/tests/test_banded_ode_solvers.py,sha256=w_nO9OxOC9HtT-QpBlfumrzDUsrBAqxa9cWpm5b7ZjE,6728
scipy/integrate/_rules/_gauss_legendre.py,sha256=KJSMmztXRqTvpmkB-ky-WSVIqAMg_GcWoewTcRxJ1Cw,1733
scipy/integrate/_rules/_base.py,sha256=AWdkCdJTmI8m_jUGv7MAhuwKBySGzVwf0GP4b3qh7-s,17931
scipy/integrate/_rules/_genz_malik.py,sha256=104fosqAnmCI992oY-Z9V_QiuG2ruWLmGS2U_EdshEw,7308
scipy/integrate/_rules/__init__.py,sha256=JNlDLTPYR-FVDeWbm9BHOot47OA8tvOj22g2iJlEsBg,328
scipy/integrate/_rules/_gauss_kronrod.py,sha256=ULpHMJRd0J99IFwNufur9BYG8EQhxlGj-OdCBgnE8yk,8473
scipy/integrate/_ivp/bdf.py,sha256=tTN2OiFRjGlIT-PkrCLi-mBfUmcAZ8NEprFSjwR_K5U,17501
scipy/integrate/_ivp/ivp.py,sha256=DGmLGk4TbhkGhBiJvnbeNScZzLdnm-6nJoWt83hrz-s,31743
scipy/integrate/_ivp/dop853_coefficients.py,sha256=OrYvW0Hu6X7sOh37FU58gNkgC77KVpYclewv_ARGMAE,7237
scipy/integrate/_ivp/__init__.py,sha256=gKFR_pPjr8fRLgAGY5sOzYKGUFu2nGX8x1RrXT-GZZc,256
scipy/integrate/_ivp/common.py,sha256=GVKTcx-QO7WPr2ejNAi94aEdMv03zFVOr24Q1w2rZ2I,15745
scipy/integrate/_ivp/radau.py,sha256=0KpFk0Me857geCXbbvAyTkqbrO8OI_2kLTdzGLpqYlY,19676
scipy/integrate/_ivp/lsoda.py,sha256=t5t2jZBgBPt0G20TOI4SVXuGFAZYAhfDlJZhfCzeeDo,9927
scipy/integrate/_ivp/rk.py,sha256=-l1jAJF_T5SeaZsRb1muFHFZ1cYUfVXZQNydMwOJEFY,22800
scipy/integrate/_ivp/base.py,sha256=Mlef_dgmn0wzjFxZA3oBbtHrQgrfdZw_8k1mLYNZP4A,10295
scipy/integrate/_ivp/tests/test_ivp.py,sha256=A0hw3AqENXeTFp1Rcb_4ayEsLYLuMVFz9s7UrglatLQ,42823
scipy/integrate/_ivp/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/integrate/_ivp/tests/test_rk.py,sha256=K9UxZghBzSL2BzmgLndPJcWOWV4Nr530TGKWakpsoeM,1326
scipy/constants/_constants.py,sha256=1OBL3gWWsaid_3eR8t7DvzE-sN8B_AKiSUCY4PZOztM,10497
scipy/constants/constants.py,sha256=w7sGxSidD2Q9Ged0Sn1pnL-qqD1ssEP1A8sZWeLWBeI,2250
scipy/constants/__init__.py,sha256=1Iqylk8TvAxegNKIcFIUVXwiH5ItKpdKtCcVPhEBvPQ,14839
scipy/constants/_codata.py,sha256=fIhZGWMCGLGSwO3rnNmDEisAN1rGLwkNbSlwdZDpowQ,202354
scipy/constants/codata.py,sha256=ThmW8ohzndi-4-WtyVXxSrW40MnLIz1XoqRcm2RgSHw,614
scipy/constants/tests/test_codata.py,sha256=AKabbXFbMwLw-SQKethXND34uJ5y_HUA20DgOzqSvsg,2841
scipy/constants/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/constants/tests/test_constants.py,sha256=G4ffHfFeFMIXUtQI8Kd7wZdrfNCr1sJx2-4H9-mCFzE,4675
scipy/datasets/_fetchers.py,sha256=4sdEEQpTI99QCR9DoLv_D6Dwd4N9cSLRJX8cENX_QCg,6735
scipy/datasets/__init__.py,sha256=X_9AbefPK1_pg-eG7g3nn--JhoHeDsrEFbJfbI5Hyak,2802
scipy/datasets/_registry.py,sha256=br0KfyalEbh5yrQLznQ_QvBtmN4rMsm0UxOjnsJp4OQ,1072
scipy/datasets/_download_all.py,sha256=iRPR2IUk6C3B5u2q77yOhac449MRSoRaTlCy2oCIknE,1701
scipy/datasets/_utils.py,sha256=kdZ-Opp7Dr1pCwM285p3GVjgZTx_mKWCvETur92FWg4,2967
scipy/datasets/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/datasets/tests/test_data.py,sha256=6DJtyMDmwi_ghOrDuryVakZQExFq-MIKiuJi_Cr7kdM,4213
scipy/io/wavfile.py,sha256=zISeQssvUbZ1kJTqrFX0x8N8QWuriM7F_KPQvaqXPQ4,28647
scipy/io/_mmio.py,sha256=Pk9Qmf4r-g7-ZQE9cCsu9_BaqiQJDRcnYlJL840WeQo,32094
scipy/io/harwell_boeing.py,sha256=BzISbfgVnrO3vYx-mP2xkLqh9r3oq64NNPbEY03P6v0,538
scipy/io/idl.py,sha256=A1QV5h6xBa1cTIejjsc1NfjG0MqMbxqFqXicC2OLNrM,504
scipy/io/__init__.py,sha256=XegFIpTjKz9NXsHPLcvnYXT-mzUrMqPJUD7a8dhUK_0,2735
scipy/io/_netcdf.py,sha256=wSulfl-YWbyIxhwF4w5gDpINzUAsvOXRXa4rWHSz8p0,39223
scipy/io/netcdf.py,sha256=RKhmlybZwbFNKA4US6xLX6O2IUDCmdkToosPt4bAUX0,533
scipy/io/_test_fortran.cpython-313-darwin.so,sha256=VFVuni2-LWSa1IUf30XxUh1RF2GGd6xxJOyqxnzQytQ,91024
scipy/io/_fortran.py,sha256=pgbB0LbOKEfPk07y-9IQXUyT7Kx_wHP0AyGPLtC53yM,10893
scipy/io/mmio.py,sha256=Dc5HqR8BXOD0wir63VTVczuZcLjSxEjbSbeZd4y27po,526
scipy/io/_idl.py,sha256=-31PPsVEtNR8It3clEfZuGRCzeBrB9OSQdkeOwNpsu0,27075
scipy/io/arff/__init__.py,sha256=czaV8hvY6JnmEn2qyU3_fzcy_P55aXVT09OzGnhJT9I,805
scipy/io/arff/_arffread.py,sha256=uOomT89u1pVrDdGKujArTE_e6Xz3Cw2f2ACPTPS6DlY,25752
scipy/io/arff/arffread.py,sha256=KW6mASZrW2J1wmC3GYucy1EO7y-rg5MgcGDMyMTpfw4,575
scipy/io/arff/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/arff/tests/test_arffread.py,sha256=NMOdsNI8uL1FJ2RB1hpi8RtNwlnIFWL1ENnvHVQLC9s,13158
scipy/io/arff/tests/data/test9.arff,sha256=ZuXQQzprgmTXxENW7we3wBJTpByBlpakrvRgG8n7fUk,311
scipy/io/arff/tests/data/nodata.arff,sha256=DBXdnIe28vrbf4C-ar7ZgeFIa0kGD4pDBJ4YP-z4QHQ,229
scipy/io/arff/tests/data/test5.arff,sha256=2Q_prOBCfM_ggsGRavlOaJ_qnWPFf2akFXJFz0NtTIE,365
scipy/io/arff/tests/data/test4.arff,sha256=mtyuSFKUeiRR2o3mNlwvDCxWq4DsHEBHj_8IthNzp-M,238
scipy/io/arff/tests/data/missing.arff,sha256=ga__Te95i1Yf-yu2kmYDBVTz0xpSTemz7jS74_OfI4I,120
scipy/io/arff/tests/data/test8.arff,sha256=c34srlkU8hkXYpdKXVozEutiPryR8bf_5qEmiGQBoG4,429
scipy/io/arff/tests/data/test11.arff,sha256=G-cbOUUxuc3859vVkRDNjcLRSnUu8-T-Y8n0dSpvweo,241
scipy/io/arff/tests/data/test3.arff,sha256=jUTWGaZbzoeGBneCmKu6V6RwsRPp9_0sJaSCdBg6tyI,72
scipy/io/arff/tests/data/iris.arff,sha256=fTS6VWSX6dwoM16mYoo30dvLoJChriDcLenHAy0ZkVM,7486
scipy/io/arff/tests/data/test2.arff,sha256=COGWCYV9peOGLqlYWhqG4ANT2UqlAtoVehbJLW6fxHw,300
scipy/io/arff/tests/data/test10.arff,sha256=va7cXiWX_AnHf-_yz25ychD8hOgf7-sEMJITGwQla30,199009
scipy/io/arff/tests/data/quoted_nominal.arff,sha256=01mPSc-_OpcjXFy3EoIzKdHCmzWSag4oK1Ek2tUc6_U,286
scipy/io/arff/tests/data/test1.arff,sha256=nUFDXUbV3sIkur55rL4qvvBdqUTbzSRrTiIPwmtmG8I,191
scipy/io/arff/tests/data/quoted_nominal_spaces.arff,sha256=bcMOl-E0I5uTT27E7bDTbW2mYOp9jS8Yrj0NfFjQdKU,292
scipy/io/arff/tests/data/test7.arff,sha256=rxsqdev8WeqC_nKJNwetjVYXA1-qCzWmaHlMvSaVRGk,559
scipy/io/arff/tests/data/test6.arff,sha256=V8FNv-WUdurutFXKTOq8DADtNDrzfW65gyOlv-lquOU,195
scipy/io/_fast_matrix_market/_fmm_core.cpython-313-darwin.so,sha256=R0nUwkkJwwvOIbnrsuV_d2xpbIogZDc9bYQGQH_3yeY,2062400
scipy/io/_fast_matrix_market/__init__.py,sha256=EmT5UuApydDttAWNYvZw3lbBuJMkw73dloawtX0o3uQ,17123
scipy/io/tests/test_netcdf.py,sha256=0OR5kfTlx9SonwZPT9P8gRz7p0HEZy_6Jwr7PkfXrpY,19459
scipy/io/tests/test_wavfile.py,sha256=1E9LMmsbEXMbzyLaqXtV_pTBa_wAX2PSaV3cJ0xamCw,16851
scipy/io/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/tests/test_idl.py,sha256=2QpZGBWoSCwH5jchc9wvot2L03p0qqeqzjqux5KP-bM,20569
scipy/io/tests/test_paths.py,sha256=3f12UO-N11JJjkw8jBgVAhz5KVrkokJbHrnvfklDhNA,3190
scipy/io/tests/test_mmio.py,sha256=ZJR9mGlYDHOQv97lp_P0XuTSmEkruqD0UNXzH9IFQeo,29039
scipy/io/tests/test_fortran.py,sha256=0cUeyIczUhtaRMFPTqHwH1U_Rm1djCaD1vDbi-6DRBo,8609
scipy/io/tests/data/scalar_string.sav,sha256=AQ7iZ8dKk9QfnLdP9idKv1ojz0M_SwpL7XAUmbHodDQ,2124
scipy/io/tests/data/array_float32_1d.sav,sha256=A_xXWkfS1sQCxP4ONezeEZvlKEXwZ1TPG2rCCFdmBNM,2628
scipy/io/tests/data/test-8000Hz-le-3ch-5S-45bit.wav,sha256=e97XoPrPGJDIh8nO6mii__ViY5yVlmt4OnPQoDN1djs,134
scipy/io/tests/data/array_float32_pointer_5d.sav,sha256=gRVAZ6jeqFZyIQI9JVBHed9Y0sjS-W4bLseb01rIcGs,7960
scipy/io/tests/data/example_3_maskedvals.nc,sha256=P9N92jCJgKJo9VmNd7FeeJSvl4yUUFwBy6JpR4MeuME,1424
scipy/io/tests/data/test-8000Hz-le-1ch-1byte-ulaw.wav,sha256=BoUCDct3GiY_JJV_HoghF3mzAebT18j02c-MOn19KxU,70
scipy/io/tests/data/array_float32_pointer_4d.sav,sha256=cXrkHHlPyoYstDL_OJ15-55sZOOeDNW2OJ3KWhBv-Kk,6680
scipy/io/tests/data/test-8000Hz-le-3ch-5S-36bit.wav,sha256=oiMVsQV9-qGBz_ZwsfAkgA9BZXNjXbH4zxCGvvdT0RY,120
scipy/io/tests/data/test-8000Hz-le-3ch-5S-24bit.wav,sha256=yCv0uh-ux_skJsxeOjzog0YBk3ZQO_kw5HJHMqtVyI0,90
scipy/io/tests/data/test-8000Hz-le-3ch-5S-24bit-rf64.wav,sha256=iSGyqouX53NaEB33tzKXa11NRIY97GG40_pqWF_k5LQ,126
scipy/io/tests/data/array_float32_pointer_8d.sav,sha256=Wk3Dd2ClAwWprXLKZon3blY7aMvMrJqz_NXzK0J5MFY,13720
scipy/io/tests/data/invalid_pointer.sav,sha256=JmgoISXC4r5fSmI5FqyapvmzQ4qpYLf-9N7_Et1p1HQ,1280
scipy/io/tests/data/test-44100Hz-2ch-32bit-float-le.wav,sha256=H0LLyv2lc2guzYGnx4DWXU6vB57JrRX-G9Dd4qGh0hM,3586
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes.wav,sha256=9qTCvpgdz3raecVN1ViggHPnQjBf47xmXod9iCDsEik,17720
scipy/io/tests/data/struct_arrays_replicated.sav,sha256=kXU8j9QI2Q8D22DVboH9fwwDQSLVvuWMJl3iIOhUAH8,2936
scipy/io/tests/data/fortran-sf8-1x1x1.dat,sha256=Djmoip8zn-UcxWGUPKV5wzKOYOf7pbU5L7HaR3BYlec,16
scipy/io/tests/data/array_float32_7d.sav,sha256=pqLWIoxev9sLCs9LLwxFlM4RCFwxHC4Q0dEEz578mpI,3288
scipy/io/tests/data/test-44100Hz-be-1ch-4bytes.wav,sha256=KKz9SXv_R3gX_AVeED2vyhYnj4BvD1uyDiKpCT3ulZ0,17720
scipy/io/tests/data/fortran-si4-1x1x1.dat,sha256=1Lbx01wZPCOJHwg99MBDuc6QZKdMnccxNgICt4omfFM,12
scipy/io/tests/data/scalar_complex32.sav,sha256=NGd-EvmFZgt8Ko5MP3T_TLwyby6yS0BXM_OW8197hpU,2076
scipy/io/tests/data/various_compressed.sav,sha256=H-7pc-RCQx5y6_IbHk1hB6OfnhvuPyW6EJq4EwI9iMc,1015
scipy/io/tests/data/array_float32_pointer_3d.sav,sha256=a_Iyg1YjPBRh6B-N_n_BGIVjFje4K-EPibKV-bPbF7E,13816
scipy/io/tests/data/struct_pointers_replicated_3d.sav,sha256=t1jhVXmhW6VotQMNZ0fv0sDO2pkN4EutGsx5No4VJQs,2456
scipy/io/tests/data/struct_scalars_replicated_3d.sav,sha256=xVAup6f1dSV_IsSwBQC3KVs0eLEZ6-o5EaZT9yUoDZI,3240
scipy/io/tests/data/fortran-si4-15x10x22.dat,sha256=OJcKyw-GZmhHb8REXMsHDn7W5VP5bhmxgVPIAYG-Fj4,13208
scipy/io/tests/data/scalar_uint32.sav,sha256=X3RbPhS6_e-u-1S1gMyF7s9ys7oV6ZNwPrJqJ6zIJsk,2072
scipy/io/tests/data/scalar_float32.sav,sha256=EwWQw2JTwq99CHVpDAh4R20R0jWaynXABaE2aTRmXrs,2072
scipy/io/tests/data/fortran-mixed.dat,sha256=zTi7RLEnyAat_DdC3iSEcSbyDtAu0aTKwUT-tExjasw,40
scipy/io/tests/data/fortran-sf8-1x1x7.dat,sha256=L0r9yAEMbfMwYQytzYsS45COqaVk-o_hi6zRY3yIiO4,64
scipy/io/tests/data/test-48000Hz-2ch-64bit-float-le-wavex.wav,sha256=EqYBnEgTxTKvaTAtdA5HIl47CCFIje93y4hawR6Pyu0,7792
scipy/io/tests/data/fortran-sf8-1x3x5.dat,sha256=c2LTocHclwTIeaR1Pm3mVMyf5Pl_imfjIFwi4Lpv0Xs,128
scipy/io/tests/data/fortran-si4-11x1x10.dat,sha256=OesvSIGsZjpKZlZsV74PNwy0Co0KH8-3gxL9-DWoa08,448
scipy/io/tests/data/example_2.nc,sha256=wywMDspJ2QT431_sJUr_5DHqG3pt9VTvDJzfR9jeWCk,272
scipy/io/tests/data/scalar_heap_pointer.sav,sha256=JXZbPmntXILsNOuLIKL8qdu8gDJekYrlN9DQxAWve0E,2204
scipy/io/tests/data/fortran-si4-1x3x5.dat,sha256=3vl6q93m25jEcZVKD0CuKNHmhZwZKp-rv0tfHoPVP88,68
scipy/io/tests/data/fortran-si4-1x1x7.dat,sha256=Dmqt-tD1v2DiPZkghGGZ9Ss-nJGfei-3yFXPO5Acpk4,36
scipy/io/tests/data/fortran-3x3d-2i.dat,sha256=oYCXgtY6qqIqLAhoh_46ob_RVQRcV4uu333pOiLKgRM,451
scipy/io/tests/data/test-8000Hz-le-3ch-5S-53bit.wav,sha256=wbonKlzvzQ_bQYyBsj-GwnihZOhn0uxfKhL_nENCGNc,150
scipy/io/tests/data/array_float32_pointer_2d.sav,sha256=b0brvK6xQeezoRuujmEcJNw2v6bfASLM3FSY9u5dMSg,3256
scipy/io/tests/data/fortran-si4-1x1x5.dat,sha256=L1St4yiHTA3v91JjnndYfUrdKfT1bWxckwnnrscEZXc,28
scipy/io/tests/data/array_float32_6d.sav,sha256=lb7modI0OQDweJWbDxEV2OddffKgMgq1tvCy5EK6sOU,19416
scipy/io/tests/data/scalar_int32.sav,sha256=IzJwLvEoqWLO5JRaHp8qChfptlauU-ll3rb0TfDDM8Y,2072
scipy/io/tests/data/fortran-sf8-1x1x5.dat,sha256=Btgavm3w3c9md_5yFfq6Veo_5IK9KtlLF1JEPeHhZoU,48
scipy/io/tests/data/test-8000Hz-le-5ch-9S-5bit.wav,sha256=TJvGU7GpgXdCrdrjzMlDtpieDMnDK-lWMMqlWjT23BY,89
scipy/io/tests/data/test-8000Hz-le-4ch-9S-12bit.wav,sha256=1F67h8tr2xz0C5K21T9y9gspcGA0qnSOzsl2vjArAMs,116
scipy/io/tests/data/array_float32_pointer_1d.sav,sha256=sV7qFNwHK-prG5vODa7m5HYK7HlH_lqdfsI5Y1RWDyg,2692
scipy/io/tests/data/struct_arrays_byte_idl80.sav,sha256=oOmhTnmKlE60-JMJRRMv_zfFs4zqioMN8QA0ldlgQZo,1388
scipy/io/tests/data/example_1.nc,sha256=EkfC57dWXeljgXy5sidrJHJG12D1gmQUyPDK18WzlT4,1736
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-early-eof.wav,sha256=bFrsRqw0QXmsaDtjD6TFP8hZ5jEYMyaCmt-ka_C6GNk,1024
scipy/io/tests/data/struct_pointer_arrays.sav,sha256=fkldO6-RO2uAN_AI9hM6SEaBPrBf8TfiodFGJpViaqg,2408
scipy/io/tests/data/struct_inherit.sav,sha256=4YajBZcIjqMQ4CI0lRUjXpYDY3rI5vzJJzOYpjWqOJk,2404
scipy/io/tests/data/array_float32_5d.sav,sha256=VmaBgCD854swYyLouDMHJf4LL6iUNgajEOQf0pUjHjg,7896
scipy/io/tests/data/test-8000Hz-be-3ch-5S-24bit.wav,sha256=hGYchxQFjrtvZCBo0ULi-xdZ8krqXcKdTl3NSUfqe8k,90
scipy/io/tests/data/scalar_int16.sav,sha256=kDBLbPYGo2pzmZDhyl8rlDv0l6TMEWLIoLtmgJXDMkk,2072
scipy/io/tests/data/struct_arrays_replicated_3d.sav,sha256=s3ZUwhT6TfiVfk4AGBSyxYR4FRzo4sZQkTxFCJbIQMI,4608
scipy/io/tests/data/fortran-sf8-15x10x22.dat,sha256=5ohvjjOUcIsGimSqDhpUUKwflyhVsfwKL5ElQe_SU0I,26408
scipy/io/tests/data/array_float32_8d.sav,sha256=R8A004f9XLWvF6eKMNEqIrC6PGP1vLZr9sFqawqM8ZA,13656
scipy/io/tests/data/null_pointer.sav,sha256=P_3a_sU614F3InwM82jSMtWycSZkvqRn1apwd8XxbtE,2180
scipy/io/tests/data/scalar_uint16.sav,sha256=928fmxLsQM83ue4eUS3IEnsLSEzmHBklDA59JAUvGK8,2072
scipy/io/tests/data/struct_scalars.sav,sha256=LYICjERzGJ_VvYgtwJ_Up2svQTv8wBzNcVD3nsd_OPg,2316
scipy/io/tests/data/array_float32_4d.sav,sha256=Tl6erEw_Zq3dwVbVyPXRWqB83u_o4wkIVFOe3wQrSro,6616
scipy/io/tests/data/struct_arrays.sav,sha256=TzH-Gf0JgbP_OgeKYbV8ZbJXvWt1VetdUr6C_ziUlzg,2580
scipy/io/tests/data/scalar_byte_descr.sav,sha256=DNTmDgDWOuzlQnrceER6YJ0NutUUwZ9tozVMBWQmuuY,2124
scipy/io/tests/data/struct_pointer_arrays_replicated.sav,sha256=eKVerR0LoD9CuNlpwoBcn7BIdj3-8x56VNg--Qn7Hgc,2492
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-rf64.wav,sha256=GSJpCuezlvHbhP3Cr4jNWmz4zG46XZ6jci2fWtiMN0k,17756
scipy/io/tests/data/Transparent Busy.ani,sha256=vwoK3ysYo87-TwzvjerHjFjSPIGpw83jjiMDXcHPWjA,4362
scipy/io/tests/data/scalar_complex64.sav,sha256=gFBWtxuAajazupGFSbvlWUPDYK-JdWgZcEWih2-7IYU,2084
scipy/io/tests/data/array_float32_pointer_7d.sav,sha256=Rp1s8RbW8eoEIRTqxba4opAyY0uhTuyy3YkwRlNspQU,3352
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-incomplete-chunk.wav,sha256=zMnhvZvrP4kyOWKVKfbBneyv03xvzgqXYhHNxsAxDJ4,13
scipy/io/tests/data/scalar_uint64.sav,sha256=ffVyS2oKn9PDtWjJdOjSRT2KZzy6Mscgd4u540MPHC4,2076
scipy/io/tests/data/array_float32_3d.sav,sha256=U7P6As7Nw6LdBY1pTOaW9C-O_NlXLXZwSgbT3H8Z8uk,13752
scipy/io/tests/data/scalar_float64.sav,sha256=iPcDlgF1t0HoabvNLWCbSiTPIa9rvVEbOGGmE_3Ilsk,2076
scipy/io/tests/data/test-8000Hz-le-2ch-1byteu.wav,sha256=R6EJshvQp5YVR4GB9u4Khn5HM1VMfJUj082i8tkBIJ8,1644
scipy/io/tests/data/fortran-sf8-11x1x10.dat,sha256=KwaOrZOAe-wRhuxvmHIK-Wr59us40MmiA9QyWtIAUaA,888
scipy/io/tests/data/test-1234Hz-le-1ch-10S-20bit-extra.wav,sha256=h8CXsW5_ShKR197t_d-TUTlgDqOZ-7wK_EcVGucR-aY,74
scipy/io/tests/data/struct_pointers_replicated.sav,sha256=aIXPBIXTfPmd4IaLpYD5W_HUoIOdL5Y3Hj7WOeRM2sA,2304
scipy/io/tests/data/array_float32_2d.sav,sha256=qJmN94pywXznXMHzt-L6DJgaIq_FfruVKJl_LMaI8UU,3192
scipy/io/tests/data/struct_pointers.sav,sha256=Zq6d5V9ZijpocxJpimrdFTQG827GADBkMB_-6AweDYI,2268
scipy/io/tests/data/struct_pointer_arrays_replicated_3d.sav,sha256=vsqhGpn3YkZEYjQuI-GoX8Jg5Dv8A2uRtP0kzQkq4lg,2872
scipy/io/tests/data/test-8000Hz-le-3ch-5S-64bit.wav,sha256=Uu5QPQcbtnFlnxOd4zFGxpiTC4wgdp6JOoYJ2VMZIU0,164
scipy/io/tests/data/test-44100Hz-2ch-32bit-float-be.wav,sha256=gjv__ng9xH_sm34hyxCbCgO4AP--PZAfDOArH5omkjM,3586
scipy/io/tests/data/struct_scalars_replicated.sav,sha256=lw3fC4kppi6BUWAd4n81h8_KgoUdiJl5UIt3CvJIuBs,2480
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-early-eof-no-data.wav,sha256=YX1g8qdCOAG16vX9G6q4SsfCj2ZVk199jzDQ8S0zWYI,72
scipy/io/tests/data/array_float32_pointer_6d.sav,sha256=9yic-CQiS0YR_ow2yUA2Nix0Nb_YCKMUsIgPhgcJT1c,19480
scipy/io/tests/data/test-8000Hz-le-3ch-5S-24bit-inconsistent.wav,sha256=t2Mgri3h6JLQDekrwIhDBOaG46OUzHynUz0pKbvOpNU,90
scipy/io/tests/data/scalar_int64.sav,sha256=-aSHQRiaE3wjAxINwuLX33_8qmWl4GUkTH45elTkA-8,2076
scipy/io/tests/data/scalar_byte.sav,sha256=dNJbcE5OVDY_wHwN_UBUtfIRd13Oqu-RBEO74g5SsBA,2076
scipy/io/_harwell_boeing/__init__.py,sha256=90qYbBzDEoTMG8ouVLGnTU2GMsY4BYOOtwJdoKT3Zz8,164
scipy/io/_harwell_boeing/_fortran_format_parser.py,sha256=beJJq2mckeU_Hu4ZM_WvrHCICJOvghI4R4bAvOnH48Q,9025
scipy/io/_harwell_boeing/hb.py,sha256=e4FbmYCXO4omXFcMW2n6qk_Cdcwx1eKHyUD5H-B71fc,19515
scipy/io/_harwell_boeing/tests/test_fortran_format.py,sha256=hPH4AmfUmyBrDU3C_Rx3j7yaGEjefQJOai4rfxMHuV0,2383
scipy/io/_harwell_boeing/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/_harwell_boeing/tests/test_hb.py,sha256=jYbRWktqO5bgXDh8i9O_u_KDTpYQcMx_blw7Pn66Nd0,2516
scipy/io/matlab/_streams.cpython-313-darwin.so,sha256=vJWhppg99thXo8NebaUpK-7MRWj4Wr7r-HAmnMpjzkI,134832
scipy/io/matlab/_mio5.py,sha256=6wfD_hwa4KdY1-pLXgjIAQfYpZO_LCCsaVMYWaV6dUI,33637
scipy/io/matlab/_mio5_params.py,sha256=skRcKG70vOlVMSb1TO67LB5312zuOUSrcOK7mOCcUss,8201
scipy/io/matlab/_mio4.py,sha256=W9FaF7ryhbT10TEgHcuovZkm7w2zIU3tDtnb5gIlYlQ,20993
scipy/io/matlab/streams.py,sha256=0Aww9GRGGnRmiAMBAzIAXsFGySu5YCUNG-cHP1omYjI,513
scipy/io/matlab/miobase.py,sha256=3qQoq8Y7ZQpHIufUCzg6RAeaLqU3qTAozmuYbaOd7BI,565
scipy/io/matlab/mio5_params.py,sha256=2RWROlfc8RmXmcXGyM-be107Tm55ibc_U7DztJ2b4fc,593
scipy/io/matlab/_miobase.py,sha256=OpKCydtebY-dqQR6GjI_8K85Zi9ZSSNBFeyUcafTjRw,13004
scipy/io/matlab/__init__.py,sha256=z1F-sXRyay69RcZUHjWSFe0IVKNKQbbMwQMrGD8i4qI,2156
scipy/io/matlab/mio_utils.py,sha256=VZPx03BNFbrQjB1CNbDCvvXUuP0_VoNRFV1R0YoB2iw,518
scipy/io/matlab/mio5_utils.py,sha256=DYiQfx5BkyDVnK4nZ3xPa-5tbpZE7WRx4SIdBmPVfSI,520
scipy/io/matlab/_mio.py,sha256=Qa_FMP-Zid7tOFTNiNjnVrYi7YkK4hKtcGJiAv884Bw,13587
scipy/io/matlab/_byteordercodes.py,sha256=AUMjfdIARtCGqyMgDDJBGa_EncP5ioYrEzyZqXOLRxU,1983
scipy/io/matlab/_mio_utils.cpython-313-darwin.so,sha256=7rZXjvgoXdRSEK4tlikuyOm3IW6oDhDxmW8Ea6Y6Gks,94512
scipy/io/matlab/byteordercodes.py,sha256=fHZVESDgIeYzGYtRlknPQ2nUqscQQ_4FhQc_ClkjBvQ,528
scipy/io/matlab/mio.py,sha256=2b0WwgQ0rBkoJ4X0hgPl889PpR7Q0i7ibSLtTQVuTto,539
scipy/io/matlab/mio4.py,sha256=hkhpBa4p0euf2rUjJviBWJ4TJs1wkUads3mX1fgDYMc,508
scipy/io/matlab/_mio5_utils.cpython-313-darwin.so,sha256=fHNuKnPLTvGGgA51ZkNyANom1-7fkGQotWt4Noz8lS0,227984
scipy/io/matlab/mio5.py,sha256=jEFeEEkXWOhziPreDt0SqfAtOo9JMauxoODAbbXHmoQ,638
scipy/io/matlab/tests/test_streams.py,sha256=dcirMJ5slCA3eIjB9VRcGG3U2htTtXL8BiYOLvHCfds,7406
scipy/io/matlab/tests/test_miobase.py,sha256=CGefrU6m_GpOwaKr_Q93Z5zKp5nuv791kjxcNNP8iiE,1460
scipy/io/matlab/tests/test_byteordercodes.py,sha256=FCHBAxeQZlhvTXw-AO-ukwTWvpN7NzmncBEDJ1P4de4,938
scipy/io/matlab/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/matlab/tests/test_pathological.py,sha256=-Efeq2x2yAaLK28EKpai1vh4HsZTCteF_hY_vEGWndA,1055
scipy/io/matlab/tests/test_mio5_utils.py,sha256=eacgGg0TaQXOkG7iaeYovtWyjPgYCY50mHPoPjnHMTI,5389
scipy/io/matlab/tests/test_mio_funcs.py,sha256=fSDaeVPvCRBFzqjWtXR5xIv9UQ_yv6Y_Nl5D5u0HIGo,1392
scipy/io/matlab/tests/test_mio_utils.py,sha256=GX85RuLqr2HxS5_f7ZgrxbhswJy2GPQQoQbiQYg0s14,1594
scipy/io/matlab/tests/test_mio.py,sha256=GNu2ffj4NOTWgWoA08CZ9_hSHhitcz6ffYZsp52WZKU,46207
scipy/io/matlab/tests/data/testcell_7.1_GLNX86.mat,sha256=62p6LRW6PbM-Y16aUeGVhclTVqS5IxPUtsohe7MjrYo,283
scipy/io/matlab/tests/data/testemptycell_5.3_SOL2.mat,sha256=g96Vh9FpNhkiWKsRm4U6KqeKd1hNAEyYSD7IVzdzwsU,472
scipy/io/matlab/tests/data/testscalarcell_7.4_GLNX86.mat,sha256=5LX9sLH7Y6h_N_a1XRN2GuMgp_P7ECpPsXGDOypAJg0,194
scipy/io/matlab/tests/data/testsparsecomplex_7.4_GLNX86.mat,sha256=rYCaWNLXK7f_jjMc6_UvZz6ZDuMCuVRmJV5RyeXiDm8,241
scipy/io/matlab/tests/data/testdouble_6.5.1_GLNX86.mat,sha256=4hY5VEubavNEv5KvcqQnd7MWWvFUzHXXpYIqUuUt-50,272
scipy/io/matlab/tests/data/test3dmatrix_7.1_GLNX86.mat,sha256=EVj1wPnoyWGIdTpkSj3YAwqzTAm27eqZNxCaJAs3pwU,213
scipy/io/matlab/tests/data/teststringarray_4.2c_SOL2.mat,sha256=pRldk-R0ig1k3ouvaR9oVtBwZsQcDW_b4RBEDYu1-Vk,156
scipy/io/matlab/tests/data/testdouble_6.1_SOL2.mat,sha256=DpB-mVKx1gsjl-3IbxfxHNuzU5dnuku-MDQCA8kALVI,272
scipy/io/matlab/tests/data/testcellnest_6.5.1_GLNX86.mat,sha256=Gl4QA0yYwGxjiajjgWS939WVAM-W2ahNIm9wwMaT5oc,568
scipy/io/matlab/tests/data/testcellnest_7.1_GLNX86.mat,sha256=CUGtkwIU9CBa0Slx13mbaM67_ec0p-unZdu8Z4YYM3c,228
scipy/io/matlab/tests/data/testobject_6.5.1_GLNX86.mat,sha256=dq_6_n0v7cUz9YziXn-gZFNc9xYtNxZ8exTsziWIM7s,672
scipy/io/matlab/tests/data/testminus_7.4_GLNX86.mat,sha256=eXcoTM8vKuh4tQnl92lwdDaqssGB6G9boSHh3FOCkng,184
scipy/io/matlab/tests/data/testminus_6.1_SOL2.mat,sha256=2X2fZKomz0ktBvibj7jvHbEvt2HRA8D6hN9qA1IDicw,200
scipy/io/matlab/tests/data/testminus_4.2c_SOL2.mat,sha256=2914WYQajPc9-Guy3jDOLU3YkuE4OXC_63FUSDzJzX0,38
scipy/io/matlab/tests/data/testcellnest_7.4_GLNX86.mat,sha256=TeTk5yjl5j_bcnmIkpzuYHxGGQXNu-rK6xOsN4t6lX8,228
scipy/io/matlab/tests/data/testsparsecomplex_6.5.1_GLNX86.mat,sha256=e0s6cyoKJeYMArdceHpnKDvtCVcw7XuB44OBDHpoa6U,400
scipy/io/matlab/tests/data/some_functions.mat,sha256=JA736oG3s8PPdKhdsYK-BndLUsGrJCJAIRBseSIEZtM,1397
scipy/io/matlab/tests/data/testminus_7.1_GLNX86.mat,sha256=gtYNC9_TciYdq8X9IwyGEjiw2f1uCVTGgiOPFOiQbJc,184
scipy/io/matlab/tests/data/testsparsecomplex_4.2c_SOL2.mat,sha256=QMVoBXVyl9RBGvAjLoiW85kAXYJ-hHprUMegEG69A5w,294
scipy/io/matlab/tests/data/testsparsecomplex_7.1_GLNX86.mat,sha256=kgHcuq-deI2y8hfkGwlMOkW7lntexdPHfuz0ar6b3jo,241
scipy/io/matlab/tests/data/test3dmatrix_7.4_GLNX86.mat,sha256=S_Sd3sxorDd8tZ5CxD5_J8vXbfcksLWzhUQY5b82L9g,213
scipy/io/matlab/tests/data/teststructarr_6.1_SOL2.mat,sha256=vneCpWBwApBGfeKzdZcybyajxjR-ZYf64j0l08_hU84,528
scipy/io/matlab/tests/data/testcell_7.4_GLNX86.mat,sha256=NkTA8UW98hIQ0t5hGx_leG-MzNroDelYwqx8MPnO63Q,283
scipy/io/matlab/tests/data/corrupted_zlib_checksum.mat,sha256=X4dvE7K9DmGEF3D6I-48hC86W41jB54H7bD8KTXjtYA,276
scipy/io/matlab/tests/data/testbool_8_WIN64.mat,sha256=_xBw_2oZA7u9Xs6GJItUpSIEV4jVdfdcwzmLNFWM6ow,185
scipy/io/matlab/tests/data/testsparse_6.1_SOL2.mat,sha256=9Sgd_SPkGNim7ZL0xgD71qml3DK0yDHYC7VSNLNQEXA,280
scipy/io/matlab/tests/data/testmatrix_6.1_SOL2.mat,sha256=ZdjNbcIE75V5Aht5EVBvJX26aabvNqbUH0Q9VBnxBS4,216
scipy/io/matlab/tests/data/bad_miuint32.mat,sha256=CVkYHp_U4jxYKRRHSuZ5fREop4tJjnZcQ02DKfObkRA,272
scipy/io/matlab/tests/data/testmulti_7.1_GLNX86.mat,sha256=KI3H58BVj6k6MFsj8icSbjy_0Z-jOesWN5cafStLPG8,276
scipy/io/matlab/tests/data/test3dmatrix_6.1_SOL2.mat,sha256=-odiBIQAbOLERg0Vg682QHGfs7C8MaA_gY77OWR8x78,232
scipy/io/matlab/tests/data/teststringarray_7.1_GLNX86.mat,sha256=lpYkBZX8K-c4FO5z0P9DMfYc7Y-yzyg11J6m-19uYTU,203
scipy/io/matlab/tests/data/testunicode_7.4_GLNX86.mat,sha256=9-8xzACZleBkMjZnbr8t4Ncs9B6mbzrONDblPnteBPU,357
scipy/io/matlab/tests/data/test_skip_variable.mat,sha256=pJLVpdrdEb-9SMZxaDu-uryShlIi90l5LfXhvpVipJ0,20225
scipy/io/matlab/tests/data/testonechar_7.1_GLNX86.mat,sha256=m8W9GqvflfAsizkhgAfT0lLcxuegZIWCLNuHVX69Jac,184
scipy/io/matlab/tests/data/teststruct_7.1_GLNX86.mat,sha256=mCtI_Yot08NazvWHvehOZbTV4bW_I4-D5jBgJ6T9EbI,314
scipy/io/matlab/tests/data/testemptycell_7.1_GLNX86.mat,sha256=t5Ar8EgjZ7fkTUHIVpdXg-yYWo_MBaigMDJUGWEIrmU,218
scipy/io/matlab/tests/data/japanese_utf8.txt,sha256=rgxiBH7xmEKF91ZkB3oMLrqABBXINEMHPXDKdZXNBEY,270
scipy/io/matlab/tests/data/testcomplex_6.5.1_GLNX86.mat,sha256=3MEbf0zJdQGAO7x-pzFCup2QptfYJHQG59z0vVOdxl4,352
scipy/io/matlab/tests/data/teststruct_6.1_SOL2.mat,sha256=3GJbA4O7LP57J6IYzmJqTPeSJrEaiNSk-rg7h0ANR1w,608
scipy/io/matlab/tests/data/teststruct_7.4_GLNX86.mat,sha256=52qaF4HRCtPl1jE6ljbkEl2mofZVAPpmBxrm-J5OTTI,314
scipy/io/matlab/tests/data/teststringarray_6.5.1_GLNX86.mat,sha256=t4tKGJg2NEg_Ar5MkOjCoQb2hVL8Q_Jdh9FF4TPL_4g,232
scipy/io/matlab/tests/data/testonechar_7.4_GLNX86.mat,sha256=t9ObKZOLy3vufnER8TlvQcUkd_wmXbJSdQoG4f3rVKY,184
scipy/io/matlab/tests/data/teststringarray_6.1_SOL2.mat,sha256=B9IdaSsyb0wxjyYyHOj_GDO0laAeWDEJhoEhC9xdm1E,232
scipy/io/matlab/tests/data/testcell_6.1_SOL2.mat,sha256=OWOBzNpWTyAHIcZABRytVMcABiRYgEoMyF9gDaIkFe4,536
scipy/io/matlab/tests/data/broken_utf8.mat,sha256=nm8aotRl6NIxlM3IgPegKR3EeevYZoJCrYpV4Sa1T5I,216
scipy/io/matlab/tests/data/debigged_m4.mat,sha256=8QbD-LzoYbKSfOYPRRw-oelDJscwufYp5cqLfZ1hB0c,1024
scipy/io/matlab/tests/data/testemptycell_6.5.1_GLNX86.mat,sha256=2Zw-cMv-Mjbs2HkSl0ubmh_htFUEpkn7XVHG8iM32o0,472
scipy/io/matlab/tests/data/testemptycell_7.4_GLNX86.mat,sha256=5PPvfOoL-_Q5ou_2nIzIrHgeaOZGFXGxAFdYzCQuwEQ,218
scipy/io/matlab/tests/data/testsparsefloat_7.4_GLNX86.mat,sha256=hnNV6GZazEeqTXuA9vcOUo4xam_UnKRYGYH9PUGTLv8,219
scipy/io/matlab/tests/data/teststring_4.2c_SOL2.mat,sha256=cAhec51DlqIYfDXXGaumOE3Hqb3cFWM1UsUK3K_lDP8,375
scipy/io/matlab/tests/data/one_by_zero_char.mat,sha256=Z3QdZjTlOojjUpS0cfBP4XfNQI3GTjqU0n_pnAzgQhU,184
scipy/io/matlab/tests/data/testsimplecell.mat,sha256=Aoeh0PX2yiLDTwkxMEyZ_CNX2mJHZvyfuFJl817pA1c,220
scipy/io/matlab/tests/data/teststringarray_7.4_GLNX86.mat,sha256=lG-c7U-5Bo8j8xZLpd0JAsMYwewT6cAw4eJCZH5xf6E,203
scipy/io/matlab/tests/data/testmulti_7.4_GLNX86.mat,sha256=Yr4YKCP27yMWlK5UOK3BAEOAyMr-m0yYGcj8v1tCx-I,276
scipy/io/matlab/tests/data/testunicode_7.1_GLNX86.mat,sha256=KV97FCW-1XZiXrwXJoZPbgyAht79oIFHa917W1KFLwE,357
scipy/io/matlab/tests/data/testsparse_6.5.1_GLNX86.mat,sha256=jp1ILNxLyV6XmCCGxAz529XoZ9dhCqGEO-ExPH70_Pg,328
scipy/io/matlab/tests/data/teststruct_6.5.1_GLNX86.mat,sha256=fRbqAnzTeOU3dTQx7O24MfMVFr6pM5u594FRrPPkYJE,552
scipy/io/matlab/tests/data/testdouble_7.1_GLNX86.mat,sha256=N2QOOIXPyy0zPZZ_qY7xIDaodMGrTq3oXNBEHZEscw0,232
scipy/io/matlab/tests/data/malformed1.mat,sha256=DTuTr1-IzpLMBf8u5DPb3HXmw9xJo1aWfayA5S_3zUI,2208
scipy/io/matlab/tests/data/testcellnest_6.1_SOL2.mat,sha256=AeNaog8HUDCVrIuGICAXYu9SGDsvV6qeGjgvWHrVQho,568
scipy/io/matlab/tests/data/teststructnest_6.5.1_GLNX86.mat,sha256=uTkKtrYBTuz4kICVisEaG7V5C2nJDKjy92mPDswTLPE,416
scipy/io/matlab/tests/data/testobject_7.1_GLNX86.mat,sha256=3z-boFw0SC5142YPOLo2JqdusPItVzjCFMhXAQNaQUQ,306
scipy/io/matlab/tests/data/testsparse_7.4_GLNX86.mat,sha256=QbZOCqIvnaK0XOH3kaSXBe-m_1_Rb33psq8E-WMSBTU,229
scipy/io/matlab/tests/data/little_endian.mat,sha256=FQP_2MNod-FFF-JefN7ZxovQ6QLCdHQ0DPL_qBCP44Y,265
scipy/io/matlab/tests/data/teststring_7.4_GLNX86.mat,sha256=igL_CvtAcNEa1nxunDjQZY5wS0rJOlzsUkBiDreJssk,224
scipy/io/matlab/tests/data/big_endian.mat,sha256=2ttpiaH2B6nmHnq-gsFeMvZ2ZSLOlpzt0IJiqBTcc8M,273
scipy/io/matlab/tests/data/testmulti_4.2c_SOL2.mat,sha256=Zhyu2KCsseSJ5NARdS00uwddCs4wmjcWNP2LJFns2-Q,240
scipy/io/matlab/tests/data/testminus_6.5.1_GLNX86.mat,sha256=i364SgUCLSYRjQsyygvY1ArjEaO5uLip3HyU-R7zaLo,200
scipy/io/matlab/tests/data/teststring_6.1_SOL2.mat,sha256=ciFzNGMO7gjYecony-E8vtOwBY4vXIUhyug6Euaz3Kg,288
scipy/io/matlab/tests/data/logical_sparse.mat,sha256=qujUUpYewaNsFKAwGpYS05z7kdUv9TQZTHV5_lWhRrs,208
scipy/io/matlab/tests/data/testonechar_6.1_SOL2.mat,sha256=ThppTHGJFrUfal5tewS70DL00dSwk1otazuVdJrTioE,200
scipy/io/matlab/tests/data/testdouble_4.2c_SOL2.mat,sha256=MzT7OYPEUXHYNPBrVkyKEaG5Cas2aOA0xvrO7l4YTrQ,103
scipy/io/matlab/tests/data/teststring_7.1_GLNX86.mat,sha256=zo7sh-8dMpGqhoNxLEnfz3Oc7RonxiY5j0B3lxk0e8o,224
scipy/io/matlab/tests/data/test_empty_struct.mat,sha256=WoC7g7TyXqNr2T0d5xE3IUq5PRzatE0mxXjqoHX5Xec,173
scipy/io/matlab/tests/data/teststructnest_6.1_SOL2.mat,sha256=sbi0wUwOrbU-gBq3lyDwhAbvchdtOJkflOR_MU7uGKA,496
scipy/io/matlab/tests/data/testcell_6.5.1_GLNX86.mat,sha256=7111TN_sh1uMHmYx-bjd_v9uaAnWhJMhrQFAtAw6Nvk,536
scipy/io/matlab/tests/data/teststructarr_6.5.1_GLNX86.mat,sha256=gqhRpSfNNB5SR9sCp-wWrvokr5VV_heGnvco6dmfOvY,472
scipy/io/matlab/tests/data/testhdf5_7.4_GLNX86.mat,sha256=ZoVbGk38_MCppZ0LRr6OE07HL8ZB4rHXgMj9LwUBgGg,4168
scipy/io/matlab/tests/data/miuint32_for_miint32.mat,sha256=romrBP_BS46Sl2-pKWsUnxYDad2wehyjq4wwLaVqums,272
scipy/io/matlab/tests/data/testdouble_7.4_GLNX86.mat,sha256=TrkJ4Xx_dC9YrPdewlsOvYs_xag7gT3cN4HkDsJmT8I,232
scipy/io/matlab/tests/data/test3dmatrix_6.5.1_GLNX86.mat,sha256=G5siwvZ-7Uv5KJ6h7AA3OHL6eiFsd8Lnjx4IcoByzCU,232
scipy/io/matlab/tests/data/testvec_4_GLNX86.mat,sha256=GQzR3mBVS266_NBfrRC9X0dLgmeu8Jl4r4ZYMOrn1V0,93
scipy/io/matlab/tests/data/testobject_7.4_GLNX86.mat,sha256=5OwLTMgCBlxsDfiEUzlVjqcSbVQG-X5mIw5JfW3wQXA,306
scipy/io/matlab/tests/data/parabola.mat,sha256=ENWuWX_uwo4Av16dIGOwnbMReAMrShDhalkq8QUI8Rg,729
scipy/io/matlab/tests/data/testsparse_7.1_GLNX86.mat,sha256=k8QuQ_4Zu7FWTzHjRnHCVZ9Yu5vwNP0WyNzu6TuiY-4,229
scipy/io/matlab/tests/data/testmatrix_6.5.1_GLNX86.mat,sha256=OB82QgB6SwtsxT4t453OVSj-B777XrHGEGOMgMD1XGc,216
scipy/io/matlab/tests/data/testmatrix_7.1_GLNX86.mat,sha256=-TYB0kREY7i7gt5x15fOYjXi410pXuDWUFxPYuMwywI,193
scipy/io/matlab/tests/data/testonechar_4.2c_SOL2.mat,sha256=BCvppGhO19-j-vxAvbdsORIiyuJqzCuQog9Ao8V1lvA,40
scipy/io/matlab/tests/data/testsparse_4.2c_SOL2.mat,sha256=dFUcB1gunfWqexgR4YDZ_Ec0w0HffM1DUE1C5PVfDDc,223
scipy/io/matlab/tests/data/testobject_6.1_SOL2.mat,sha256=kzLxy_1o1HclPXWyA-SX5gl6LsG1ioHuN4eS6x5iZio,800
scipy/io/matlab/tests/data/testmatrix_4.2c_SOL2.mat,sha256=14YMiKAN9JCPTqSDXxa58BK6Un7EM4hEoSGAUuwKWGQ,151
scipy/io/matlab/tests/data/testonechar_6.5.1_GLNX86.mat,sha256=SBfN6e7Vz1rAdi8HLguYXcHUHk1viaXTYccdEyhhob4,200
scipy/io/matlab/tests/data/testfunc_7.4_GLNX86.mat,sha256=ScTKftENe78imbMc0I5ouBlIMcEEmZgu8HVKWAMNr58,381
scipy/io/matlab/tests/data/testcomplex_4.2c_SOL2.mat,sha256=WOwauWInSVUFBuOJ1Bo3spmUQ3UWUIlsIe4tYGlrU7o,176
scipy/io/matlab/tests/data/testcomplex_6.1_SOL2.mat,sha256=GpAEccizI8WvlrBPdvlKUv6uKbZOo_cjUK3WVVb2lo4,352
scipy/io/matlab/tests/data/teststructnest_7.4_GLNX86.mat,sha256=CNXO12O6tedEuMG0jNma4qfbTgCswAbHwh49a3uE3Yk,252
scipy/io/matlab/tests/data/nasty_duplicate_fieldnames.mat,sha256=bvdmj6zDDUIpOfIP8J4Klo107RYCDd5VK5gtOYx3GsU,8168
scipy/io/matlab/tests/data/testcomplex_7.4_GLNX86.mat,sha256=8rWGf5bqY7_2mcd5w5gTYgMkXVePlLL8qT7lh8kApn0,247
scipy/io/matlab/tests/data/teststructarr_7.1_GLNX86.mat,sha256=6VDU0mtTBEG0bBHqKP1p8xq846eMhSZ_WvBZv8MzE7M,246
scipy/io/matlab/tests/data/test_mat4_le_floats.mat,sha256=2xvn3Cg4039shJl62T-bH-VeVP_bKtwdqvGfIxv8FJ4,38
scipy/io/matlab/tests/data/teststring_6.5.1_GLNX86.mat,sha256=yrJrpLiwLvU_LI1D6rw1Pk1qJK1YlC7Cmw7lwyJVLtw,288
scipy/io/matlab/tests/data/bad_miutf8_array_name.mat,sha256=V-jfVMkYyy8qRGcOIsNGcoO0GCgTxchrsQUBGBnfWHE,208
scipy/io/matlab/tests/data/teststructnest_7.1_GLNX86.mat,sha256=o4F2jOhYyNpJCo-BMg6v_ITZQvjenXfXHLq94e7iwRo,252
scipy/io/matlab/tests/data/corrupted_zlib_data.mat,sha256=DfE1YBH-pYw-dAaEeKA6wZcyKeo9GlEfrzZtql-fO_w,3451
scipy/io/matlab/tests/data/teststructarr_7.4_GLNX86.mat,sha256=ejtyxeeX_W1a2rNrEUUiG9txPW8_UtSgt8IaDOxE2pg,246
scipy/io/matlab/tests/data/testcomplex_7.1_GLNX86.mat,sha256=VNHV2AIEkvPuhae1kKIqt5t8AMgUyr0L_CAp-ykLxt4,247
scipy/io/matlab/tests/data/sqr.mat,sha256=3DtGl_V4wABKCDQ0P3He5qfOzpUTC-mINdK73MKS7AM,679
scipy/io/matlab/tests/data/testsparsecomplex_6.1_SOL2.mat,sha256=WfEroAT5YF4HGAKq3jTJxlFrKaTCh3rwlSlKu__VjwA,304
scipy/io/matlab/tests/data/single_empty_string.mat,sha256=4uTmX0oydTjmtnhxqi9SyPWCG2I24gj_5LarS80bPik,171
scipy/io/matlab/tests/data/testmatrix_7.4_GLNX86.mat,sha256=l9psDc5K1bpxNeuFlyYIYauswLnOB6dTX6-jvelW0kU,193
scipy/io/matlab/tests/data/miutf8_array_name.mat,sha256=Vo8JptFr-Kg2f2cEoDg8LtELSjVNyccdJY74WP_kqtc,208
scipy/_lib/_test_deprecation_call.cpython-313-darwin.so,sha256=O0HMKTo37AfpXmiV4CbfGBa1ymZoqsBM9ltYNF4vr60,74352
scipy/_lib/_ccallback_c.cpython-313-darwin.so,sha256=kPAoi-QTyAT-zHwrevFkyaUCM0e_ATUdzfANjZgOiv8,115952
scipy/_lib/uarray.py,sha256=4X0D3FBQR6HOYcwMftjH-38Kt1nkrS-eD4c5lWL5DGo,815
scipy/_lib/_testutils.py,sha256=5Ua6vjKp02oRGpWX1icBHh1NjlgVCPRIVIrdgb9VSyc,12067
scipy/_lib/_pep440.py,sha256=vo3nxbfjtMfGq1ektYzHIzRbj8W-NHOMp5WBRjPlDTg,14005
scipy/_lib/_disjoint_set.py,sha256=o_EUHZwnnI1m8nitEf8bSkF7TWZ65RSiklBN4daFruA,6160
scipy/_lib/deprecation.py,sha256=2xwTeh_7Uc71zmnJW264zxjvh0LUWQqZsH6s95dQDyo,9840
scipy/_lib/_array_api.py,sha256=ydofyemfVuger2iLfVlUA2pseolDonoxypdAAQSTtBc,22353
scipy/_lib/_array_api_no_0d.py,sha256=zVB7D070dZ9Rc-7mXvlkqpv75TgcvCy_7PL0q6yZsbg,4453
scipy/_lib/decorator.py,sha256=-Rm0CvawUDXzPssHjts9vrDAC57_d_x4IfOAzgf19SQ,15021
scipy/_lib/_bunch.py,sha256=WooFxHL6t0SwjcwMDECM5wcWWLIS0St8zP3urDVK-V0,8120
scipy/_lib/_fpumode.cpython-313-darwin.so,sha256=y6kwIgAv3_N3MLCK7EdNXkj3qSYYZfD6lbaQiz8R538,50208
scipy/_lib/__init__.py,sha256=CXrH_YBpZ-HImHHrqXIhQt_vevp4P5NXClp7hnFMVLM,353
scipy/_lib/_finite_differences.py,sha256=llaIPvCOxpE4VA8O8EycPEU8i6LHJyOD-y7Y9OvQHt0,4172
scipy/_lib/_ccallback.py,sha256=N9CO7kJYzk6IWQR5LHf_YA1-Oq48R38UIhJFIlJ2Qyc,7087
scipy/_lib/_threadsafety.py,sha256=ttPEh64SKLjhQGZIYSm_9d5bW4cjAXoRZCA_a5-nK9M,1453
scipy/_lib/_tmpdirs.py,sha256=z3IYpzACnWdN_BMjOvqYbkTvYyUbfbQvfehq7idENSo,2374
scipy/_lib/_elementwise_iterative_method.py,sha256=79M1Rrgx01KoBKAgxjnY_QwbVerbnt_UpmgOYt97pwg,15277
scipy/_lib/_test_deprecation_def.cpython-313-darwin.so,sha256=pkpv_PVYj_AaAqEE5ZffTxi_UVsbSXsUaLG-sbQ1p78,56008
scipy/_lib/_util.py,sha256=yEp-zOqfklOTMcvzAL0S9dTffhuJDOiYchIYxWBkbFE,44605
scipy/_lib/_gcutils.py,sha256=hajQd-HUw9ckK7QeBaqXVRpmnxPgyXO3QqqniEh7tRk,2669
scipy/_lib/_docscrape.py,sha256=OUfg01moyk_U05boFoyiwKdpUe44iiqKcSkKVHNQsYY,23808
scipy/_lib/_test_ccallback.cpython-313-darwin.so,sha256=MZ5mU9K9OdyORdy7tnca2D2ode_RJoeuOjugRZNeEKM,52968
scipy/_lib/messagestream.cpython-313-darwin.so,sha256=TcJgFpwZp0xWcb_PQYqbD1UMSAwCcyHIguUtDjT1-4w,97360
scipy/_lib/doccer.py,sha256=dzTRxBKnbl1wSILhYgrAj3-V0i0JvK-UhaWP0xJ7NpI,10907
scipy/_lib/_uarray/_uarray.cpython-313-darwin.so,sha256=ZM5WKVkuzgEG5H56UZXiHPHWokGrEg2Uh9DigQcy_fE,120800
scipy/_lib/_uarray/LICENSE,sha256=yAw5tfzga6SJfhTgsKiLVEWDNNlR6xNhQC_60s-4Y7Q,1514
scipy/_lib/_uarray/_backend.py,sha256=LZnSLJ2UK209jrMtocOMoc5grlNoob3tbb1HbW0XlAQ,20531
scipy/_lib/_uarray/__init__.py,sha256=Rww7wLA7FH6Yong7oMgl_sHPpjcRslRaTjh61W_xVg4,4493
scipy/_lib/array_api_compat/_internal.py,sha256=0GHLUJRbBHZLsbgRYE0OCtxAKdYuLtr1qzh70N5vBQI,1010
scipy/_lib/array_api_compat/__init__.py,sha256=jjRoCLlFhQjrHK2xCR3aHoUVjovGKMBSBsHZmi6yjjI,969
scipy/_lib/array_api_compat/dask/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/_lib/array_api_compat/dask/array/_info.py,sha256=D8hG1uRNsF31_WX5bnulbdl75Jkd6G2DbkmhXXTplEs,10410
scipy/_lib/array_api_compat/dask/array/__init__.py,sha256=7_FttjbrGeKtPFGS_CA85WZZmbxPwkpxvsMS8KTMEFw,242
scipy/_lib/array_api_compat/dask/array/_aliases.py,sha256=ERdXHmeTGKxBMSiSt_VlxsnZ0sLh9K8bxkWQT1OKqMM,6549
scipy/_lib/array_api_compat/dask/array/linalg.py,sha256=5E3wSAXmiZJ5rf69u6Pzw1Xs0lCdMpiVBnheA4lzY4E,2441
scipy/_lib/array_api_compat/dask/array/fft.py,sha256=FWXfXVz9zUGKVtYJWl-xSb9BUp7UIewQ89FzGimwOOA,553
scipy/_lib/array_api_compat/cupy/_typing.py,sha256=oDhrZB8R-D6wvee7tR4YkyBhTq93M0fFi3Tv-lpN_Dg,617
scipy/_lib/array_api_compat/cupy/_info.py,sha256=kdUS8xcIVg_0Mgg2qSzuqOrXgopaHO_G8JmGBB-4qOM,9805
scipy/_lib/array_api_compat/cupy/__init__.py,sha256=3079YH9uF2HoG8E27bp_1lsIVvYsdrq8hKMk_jT3NFs,442
scipy/_lib/array_api_compat/cupy/_aliases.py,sha256=aCmWDlvcdhagje7QDxgF-jqTmUk6mnVIl2hOky1IpBE,4538
scipy/_lib/array_api_compat/cupy/linalg.py,sha256=nKOM-_wcOHzHhEeV9KBzcMVNlviJK4nP1nFBUtvnjTM,1444
scipy/_lib/array_api_compat/cupy/fft.py,sha256=xCAC42CNAwAyVW7uCREsSoAV23R3rL2dqrT7w877zuE,842
scipy/_lib/array_api_compat/torch/_info.py,sha256=rnInxwjMErvcHLI4S6fzom7N43hoAqS0rysw1K8Riyw,11413
scipy/_lib/array_api_compat/torch/__init__.py,sha256=sk32NV12KrlR8a-UjiBdjJspUcex5j7REAGgSJoI3do,591
scipy/_lib/array_api_compat/torch/_aliases.py,sha256=kCIeFyzzUqNh86Byo5Ai2s1guK2-OkXg62chBCN_kgU,28559
scipy/_lib/array_api_compat/torch/linalg.py,sha256=dJ0o1gCbSDtklpvgZCxx3gbHXW9q3I4u8ZLFPW24dJs,4770
scipy/_lib/array_api_compat/torch/fft.py,sha256=AVHOwIxM-t9_w-FjVF79RrzeC5wYc5g97WPUp7bIHlA,1794
scipy/_lib/array_api_compat/numpy/_typing.py,sha256=OFRXfhT8-snL_4VeOjbOCd_yYIGqVS-IRrZoWNcL3v4,618
scipy/_lib/array_api_compat/numpy/_info.py,sha256=GAD-zNvAMUSeUJfjABY6p_eYkG--KBBgz1vdQkL2-UA,10384
scipy/_lib/array_api_compat/numpy/__init__.py,sha256=uxjYAO4xcDhTQPbrD2XmkWT5TyZsjpwc5FD-ViHxN-c,831
scipy/_lib/array_api_compat/numpy/_aliases.py,sha256=ZrddTjHOVUNvDM1h9p7NqXqaODVJKkKu2fTyPClCmXg,4485
scipy/_lib/array_api_compat/numpy/linalg.py,sha256=ne4h3Ui1esyzD9p7Ko2IueJvgpSUmfF_Z5aWbiBKJc0,3256
scipy/_lib/array_api_compat/numpy/fft.py,sha256=vlrYUcv2VV5mOOEb5R4u83nFSSDmE-nfJYM-lmq1Dao,679
scipy/_lib/array_api_compat/common/_fft.py,sha256=qZvAveqXFwEQxCbTNx9l_41EpQpAwMfwS2GqWKEVwow,4520
scipy/_lib/array_api_compat/common/_typing.py,sha256=KBJcLRAG2MeID9V38-GBipfpsFWGGrxOKkgfSQmgjXE,414
scipy/_lib/array_api_compat/common/__init__.py,sha256=HB4vvyS0GnH6JQSEgAC75oa-s2WBIiQQebpgXnW00N0,37
scipy/_lib/array_api_compat/common/_linalg.py,sha256=BebUx7WRkz9DAx9lrrP8d57-uN0VobwLGX0xbvI-7Wg,6142
scipy/_lib/array_api_compat/common/_aliases.py,sha256=Vr_64oTgASVrbawHA2oJjJhYXLPx7tXii8vFPyG8D98,17875
scipy/_lib/array_api_compat/common/_helpers.py,sha256=4gXCgC9TRmgFlXxfHtznk6Jv7MOZ03e3xE1f7jQKaC0,23956
scipy/_lib/tests/test_deprecation.py,sha256=pIia1qGES_ABOfbqLSSlXzmLmeBjpziyvh9J2mUUcMA,390
scipy/_lib/tests/test__pep440.py,sha256=u9hPoolK4AoIIS-Rq74Du5SJu5og2RxMwgaAvGgWvRo,2277
scipy/_lib/tests/test_warnings.py,sha256=ZQ_4o16m2b--0v8erteoUd2pA134GzMRZhTV9vfuhqI,4949
scipy/_lib/tests/test__util.py,sha256=-66scvDLNcoXLcVVSBFP4l6J0htlJvCEoNPHJJvMkVI,24645
scipy/_lib/tests/test__threadsafety.py,sha256=qSfCF5OG_5lbnSl-grmDN_QCU4QLe-fS3sqnwL04pf8,1322
scipy/_lib/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/_lib/tests/test_doccer.py,sha256=2HGlzqu7dgJ7collFy6SunjKc4lKMFo4TZIUQCHlVoU,4053
scipy/_lib/tests/test_scipy_version.py,sha256=kVoxuBUidCHsVpvybRPoVJzkv2hUixRwuDAEAqPgpaA,918
scipy/_lib/tests/test_bunch.py,sha256=sViE5aFSmAccfk8kYvt6EmzR5hyQ9nOSWMcftaDYDBg,6168
scipy/_lib/tests/test_ccallback.py,sha256=dy9g70zyd80KpawffSKgWbddsKUwNNeF5sbxMfCTk6w,6175
scipy/_lib/tests/test_import_cycles.py,sha256=K4LfxIHzFRIj4XGGmpRhYj4Kij8GXYxKGbIX8WfjUWQ,586
scipy/_lib/tests/test_array_api.py,sha256=wAPTUd0GySv5pQXTN7Cn75-tETWYM46y473UCxXn-nQ,8075
scipy/_lib/tests/test__gcutils.py,sha256=Uadt4yXwuLDMCSbf4cpMszR_5NOeVQC1E_v4NZAeJR4,3729
scipy/_lib/tests/test_tmpdirs.py,sha256=DiSY_ReQtD9Ou01pJ49MVY1aT6L62W2Odbbr-zEm3zI,1337
scipy/_lib/tests/test_config.py,sha256=ekM39jzkDFcuk3ahIMn-j4JUz3kZeSDxxB_2WRRxULM,1275
scipy/_lib/tests/test__testutils.py,sha256=P4WDJpUgy19wD9tknQSjIivuQvZF7YUBGSBWlur2QRA,800
scipy/_lib/tests/test_public_api.py,sha256=ZB6xJ_-qVr1paESyx0MMGJQSxdFPqJeHs2BWiwQeeUk,18066
scipy/_lib/array_api_extra/_typing.py,sha256=E3XJz5PbjXP-ckQMQLi_nOJPLr-B0cm_EVArRwY-7FY,193
scipy/_lib/array_api_extra/__init__.py,sha256=916j5GLpulyZZsUQa-I_r510XDVbap_aIrVpCVn_PIk,266
scipy/_lib/array_api_extra/_funcs.py,sha256=T5nPgBxYOb8DkNHlEM52Qf70Nf7Qb6lFtlDtuvmEk4c,14906
scipy/_lib/cobyqa/models.py,sha256=cAM8_np_xFSRwKsjaMRZu9Dc9xQOQPAZVWxsvR_7qjE,50656
scipy/_lib/cobyqa/framework.py,sha256=lIeKCkDLxHbMmSTiMcyasvVe77jVvh_YTOYX0HnK4Qk,38900
scipy/_lib/cobyqa/__init__.py,sha256=9Gj-EtpYGRmh0-ADiX0t0psItcvMgzIMwFDzlvOzcE8,578
scipy/_lib/cobyqa/problem.py,sha256=SiPgmiFTxiW5yJ_FVf37Z9GQGo6Gx_fJ3RXMzhsrn40,40203
scipy/_lib/cobyqa/settings.py,sha256=ogfiShxuPHsMfW16OGSwB9-mIPRiuWZSGXBOCO2HDvw,3826
scipy/_lib/cobyqa/main.py,sha256=wz0M2iqFfzeTaZUq_j1TkF_9V_SJ1t73A-0fdH0eSs4,57527
scipy/_lib/cobyqa/subsolvers/__init__.py,sha256=VmFBpi-_tNa8yzNmu_fufewmPTnCU6ycNCGcN34UBcc,341
scipy/_lib/cobyqa/subsolvers/geometry.py,sha256=dgS-C0QBUhkzPhHULFIRbnbFOIEB005GyPYE-i-cuFY,14173
scipy/_lib/cobyqa/subsolvers/optim.py,sha256=hIseVqrPyI3ezICGNXkCtKlpqvAO2W6ZQe0n7sxfkss,45512
scipy/_lib/cobyqa/utils/__init__.py,sha256=sw6g402vXaXwX7rMhxrNl5PD5OBs89l5f3XNcYApRHs,359
scipy/_lib/cobyqa/utils/exceptions.py,sha256=N1JdmUxHnME95wEZHyeeF_M6GXPEqH5t3qzuXig49YE,483
scipy/_lib/cobyqa/utils/versions.py,sha256=eBOlEGAKFCfjFqVprdali3M1G7l0k_kxb7ku-Lz2bU0,1465
scipy/_lib/cobyqa/utils/math.py,sha256=beT-Tib41TJWZecjnKhSfu4foOLLaHlWj5CcyRhdSl4,1611
scipy/special/_spfun_stats.py,sha256=IjK325nhaTa7koQyvlVaeCo01TN9QWRpK6mDzkuuAq0,3779
scipy/special/_sf_error.py,sha256=q_Rbfkws1ttgTQKYLt6zFTdY6DFX2HajJe_lXiNWC0c,375
scipy/special/spfun_stats.py,sha256=ESJXGUwH7iijUk6aXZQVI1pnaWiVZ6_l0hVpC4bBSIw,535
scipy/special/_input_validation.py,sha256=ZEwg_sZaesaqzaVA_btZQAi_uPXtIViL_u3Zms6UnyQ,474
scipy/special/_specfun.cpython-313-darwin.so,sha256=f0RWoHT2uWiSPuGRwZGi2uE4QjlwQ4Q-qIiUHEBpLyk,227008
scipy/special/_test_internal.cpython-313-darwin.so,sha256=9JqTtrlIw2h31s9E3zIzMg-GRoymaI0hYJqkx87Zc1E,208904
scipy/special/_comb.cpython-313-darwin.so,sha256=NNWT0tSmgkFPBaDr9vMSi0FgB0vgfEhBTPh7ITsfwqk,77304
scipy/special/_testutils.py,sha256=o_h6MBVRhEubUC7flB-1LLr1GF5GJgVw9iol46H2lPs,11975
scipy/special/cython_special.pyi,sha256=BQVUCzV8lCylnmLCtnN0Yz_ttlqyzcLc-BZx2KPXPzM,58
scipy/special/_ufuncs_cxx_defs.h,sha256=X8HIX3AK-7HXPIAPN1KGw5KOdF5GTvMmlR4Sl9nLwFU,9609
scipy/special/_add_newdocs.py,sha256=ZGPOb0r2gI8MIG9SA7_dEleWl8CHFprVyt422UabbQ8,290517
scipy/special/_ufuncs.cpython-313-darwin.so,sha256=UxegObuRuLU8PsLrfwhs5rubMRl6Ys13fZAIfeUepfc,828816
scipy/special/sf_error.py,sha256=wOZqzX7iipkH39hOHqBlkmretJRbYy-K7PsnZPyaJFU,573
scipy/special/cython_special.cpython-313-darwin.so,sha256=4PN8RmncRxu2zYI_A9hX1HsgdqEMTJWqJk28D8I1pA0,2281952
scipy/special/add_newdocs.py,sha256=Wnd-5R0wQAVxSolD4QY2CamTSbe1k48Aie3XaBWRKKc,436
scipy/special/_spherical_bessel.py,sha256=E6aFHez6Ev8sUlJNLKWk5pZ0bwIp3vrafZr8Bh2Vws8,12446
scipy/special/_test_internal.pyi,sha256=cye6-VI7Jxvb4JDfa1R_f7slEDjYUUfM4edFZ_e0XiE,394
scipy/special/__init__.pxd,sha256=l9Y21wnx5fZLvrxCeCMUWQvBI5gHx7LBhimDWptxke8,42
scipy/special/_orthogonal.pyi,sha256=a0iJfx1CdwZQjf2o8RfM7jiS2daOfXSwQ4a2hpoFhVs,8242
scipy/special/_mptestutils.py,sha256=ocy_wBXqHGIg311jfjATEA8O29ICl4qPnvTgsmTm5qg,14441
scipy/special/cython_special.pxd,sha256=6dBzCjT38uzfixyM49cTuB6zfUH69m2DGN2WBVVBk9I,16362
scipy/special/__init__.py,sha256=DoBkidFI8n9vihdtuv6XB_VBiz750909thSvHTOAXVs,33726
scipy/special/_ufuncs_cxx.pxd,sha256=Ltt2eonXvAbhRTKQj74VH299NBK9mCx4XYCdyUXLQ4U,5644
scipy/special/_ufuncs.pyi,sha256=AIHP4TNIs1CeqhIgObHyY0S2nNGBo6cICL_3hpRzj9o,8839
scipy/special/_ellip_harm.py,sha256=YHHFZXMtzdJxyjZXKsy3ocIsV-eg6ne3Up79BuFl9P8,5382
scipy/special/_ufuncs_cxx.pyx,sha256=Py0yENPlxWqfc700rtXPv2ZTrL8tnh1HR-K_vWlbCKU,31470
scipy/special/_lambertw.py,sha256=-oSEnHFQWZiUZXMamxPWjfntWq5tt0rzHmI13DxGHBY,3962
scipy/special/_ufuncs_cxx.cpython-313-darwin.so,sha256=e63tyPi17gieYX7HpE5cwFYtZGyPtmgObC_X6N2eoRc,1350496
scipy/special/basic.py,sha256=LRU8rIxXx42O4eVZv21nFwswAu7JFtQ42_4xT5BwYpE,1582
scipy/special/libsf_error_state.dylib,sha256=2NWvBWbYl-llbX7z5TTshTtqYb17p1pdBJSSToqSDCE,33448
scipy/special/_gufuncs.cpython-313-darwin.so,sha256=zKOhl1rp148CY1VyEsPkDMMfm-VRop6K08DOFLZdyEc,645312
scipy/special/_special_ufuncs.cpython-313-darwin.so,sha256=ckuM35m9wogP-5eBkXWe2-y57jYJMvXblxUPRs6arOo,1046272
scipy/special/_orthogonal.py,sha256=9RcRoMBby-UMRN8bBqK_m34b9gcAhvP3i630SzAnKJk,74230
scipy/special/_support_alternative_backends.py,sha256=3Qlio4pv6iJoZvPhilpx5YZifX3R4a39k5uHbo_Vyd8,6315
scipy/special/_ellip_harm_2.cpython-313-darwin.so,sha256=h_s7sdeA8r9aJyE_KkuEj1zbqAyJYGEg2RNYPh1r8gY,132336
scipy/special/orthogonal.py,sha256=aLzv7PzJgsdLpyTrV6Cu-rpHNHWlUAEqOImiW4fuzuE,1724
scipy/special/specfun.py,sha256=V1ZaKH1FFHPvzgkFa-UBVaVTLJRO4fodr7NAW_1jExo,588
scipy/special/_multiufuncs.py,sha256=z9UQsy0fwHF-f6tUZOFAjmhw6EXx3njzA2mkyRk-Zho,18522
scipy/special/_ufuncs_defs.h,sha256=h0MFUp-u8riZ6vm7y7UhcCzw4_kuGWxVc7q5IAAW1Ns,3166
scipy/special/_ufuncs.pyx,sha256=O98FaNvASL6ooj4ymS-Re2-1tZlzA6hyKwpUEdKWbEk,605812
scipy/special/_basic.py,sha256=8AwohnlJ1Z_396QgTh4L1Ba5iiVL_iewk_tg4CukAjU,112015
scipy/special/_logsumexp.py,sha256=EsqkmAtuVAtxZI1koHPGhvxChP9ar7pYlwVZjDTjI5s,13961
scipy/special/_precompute/wright_bessel_data.py,sha256=f1id2Gk5TPyUmSt-Evhoq2_hfRgLUU7Qu_mELKtaXGg,5647
scipy/special/_precompute/expn_asy.py,sha256=JAz0hY1gBJu3Q_dvscQrSJdgKuwpjqFZVwz-sOQQ21w,1265
scipy/special/_precompute/zetac.py,sha256=LmhJP7JFg7XktHvfm-DgzuiWZFtVdpvYzzLOB1ePG1Q,591
scipy/special/_precompute/struve_convergence.py,sha256=z7R0Q5_Ye-EqLI9g-yARdl_j5FooofXMRXPLVrIFJQQ,3624
scipy/special/_precompute/loggamma.py,sha256=iq7ZBrUmk8pXYZwO_wINI4u8ENsLbL9VUShGjGO0Pt0,1094
scipy/special/_precompute/wright_bessel.py,sha256=7z2W3spGANZO31r_xauMA6hIQ0eseRlXx-zJW6du5tU,12868
scipy/special/_precompute/gammainc_asy.py,sha256=P5OFRcPkkpjGQeYCaMZ8SFSUmZG_CjrEHv8OLwgcGFc,2502
scipy/special/_precompute/wrightomega.py,sha256=YpmLwtGJ4qazMDY0RXjhnQiuRAISI-Pr9MwKc7pZlhc,955
scipy/special/_precompute/gammainc_data.py,sha256=jogxBuXLr3uEpMBvpqScDz5TzEEalksH8f-cRGzasck,4077
scipy/special/_precompute/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/special/_precompute/utils.py,sha256=JXJuI07Jlm4bDHJFVtj0jHq05p-V1ofeXZB16Y05kzI,887
scipy/special/_precompute/cosine_cdf.py,sha256=ZGSeDDpLRsapyx2GbIrqqYR98fvaEQrLn7IE-fuodhE,354
scipy/special/_precompute/lambertw.py,sha256=7f4F3ivouVNZwuvVX8TAi2lPB7LirPS8IfN5lEw9zI0,1961
scipy/special/_precompute/hyp2f1_data.py,sha256=STSBybQ2pCAu6sh8c9tiHsoDOgnisnSp4tkP2cK4MuI,14707
scipy/special/xsf/binom.h,sha256=IOVEKVugDUr9zqCLOk99Pj9LcMiGIZe4zzJCtWlYTZg,2471
scipy/special/xsf/error.h,sha256=UR9iGZFzuTeqAlNsqTKIRK9VaD-c70CAZLquyoAuDfA,1731
scipy/special/xsf/digamma.h,sha256=dt4JcA8YOwSLvJEMwglQHDjun5xH4cRZ3NU6RQU2pKk,7515
scipy/special/xsf/expint.h,sha256=iyJ6V4PHCOnRQRY4YWqifIF1Ri56LYNcbquMT_q5gBs,8345
scipy/special/xsf/config.h,sha256=P5g5tNTQVAPx8P2bvxlEdT2shWQHXevshd5y91G7nt0,8438
scipy/special/xsf/cdflib.h,sha256=1BrCII189UOWaBsII0H1kLgHfo8wdgaoysSbPojKIGU,4176
scipy/special/xsf/lambertw.h,sha256=Eon5lhh7L4n5ycalsiNfBjt3WiM1gd8-jR40F5g4u8Q,5411
scipy/special/xsf/trig.h,sha256=ZK6mxae-JxM9o8Cf4xytP5lXWhGgGQUgtm7vxsyxV2A,4362
scipy/special/xsf/wright_bessel.h,sha256=eYkLjIiTx9iXHaAKdQXpGBWa4mmoZ0ZuQlSLGxSu53U,42619
scipy/special/xsf/tools.h,sha256=x2ZqPsfRghqo7QJBmaCs8b7rJPDzB2VPUK92ExerRlM,16145
scipy/special/xsf/sici.h,sha256=mzu3DK3oGE7o7KMjmqfmdirWvpBuFejqQu1WKbir2vo,5854
scipy/special/xsf/loggamma.h,sha256=GDJhdc7dldEiN7Xj2O5c91AgXCUkI4L_nFDO5FrAq-c,6209
scipy/special/xsf/evalpoly.h,sha256=JCz6KMNA4jDKenIfi0Z2KhVpVOb1bzzBltEz7oTOXlw,1119
scipy/special/xsf/zlog1.h,sha256=tu6rdW4hOWkrEt00KTX3BWq5kD0ZPuiCIRT7G_M1pZE,965
scipy/special/xsf/hyp2f1.h,sha256=r4T41QT5kxrx1Gysh8SZk-3CIiUiAEQBuBephEoUnEo,34738
scipy/special/xsf/iv_ratio.h,sha256=nX7K3F8LV0zFNa3CoHC5dBMl5dAO5uH16lAskqZzARM,5674
scipy/special/xsf/cephes/scipy_iv.h,sha256=Tw2Ls0PAqBbZyfbcYuzNSX6NPiYQqfuwZAw2Taty2mY,25450
scipy/special/xsf/cephes/igam_asymp_coeff.h,sha256=ky3gnc7fifHIDRtamh4h5Ex2gKdBj6WPy4rmNtqD2nc,17893
scipy/special/xsf/cephes/zeta.h,sha256=s21iDx7jlgHsOJdks6aXs2n-Z0K0A7C9Z2lLdpRtAUI,4381
scipy/special/xsf/cephes/polevl.h,sha256=7_WTjsgG9WKExZO0RSU8e0c_j6qvnWvDPYEa63Lq0Jk,4075
scipy/special/xsf/cephes/beta.h,sha256=MDaX9iQorb6nYHKIjsS10qq0PmS-h8_f-MV3XHL35UQ,6981
scipy/special/xsf/cephes/chdtr.h,sha256=eADp4we-EkfmgSRtjztWrkBhiad0LKfS4zCF5SLqth8,4047
scipy/special/xsf/cephes/psi.h,sha256=2GQCNBA4UHa-Y8bo9CE2Lm6q7HnOlOsxu1BPt9xfFdY,6291
scipy/special/xsf/cephes/i0.h,sha256=rnsastkYnz7FPozLTZXE2NjLYjRtO2bqsCrNLmBS7V4,4548
scipy/special/xsf/cephes/k1.h,sha256=NYGMytXenLXSe2RZcRds3yGfHlvQwKmpegkDuKnDH8g,4626
scipy/special/xsf/cephes/tandg.h,sha256=9Ko6moB_BLWq29XOWynKwp9XeTf6eQbotcKaIBPbrxQ,3391
scipy/special/xsf/cephes/shichi.h,sha256=wR_EwP7h-qwaqIjxb1Edn3RhhjPAEYQW5hFF1QzkMrQ,8513
scipy/special/xsf/cephes/expn.h,sha256=IiyXzwtCkUT-TRz8TnMyvdoFi3g0Ri1BThEVydX3S7g,8942
scipy/special/xsf/cephes/igam.h,sha256=w8_0jQmn-Lxtr-7NFeXKnqyo1jCRBBjup31kOJR0r0E,12877
scipy/special/xsf/cephes/j1.h,sha256=Qd9M25owFl3YOuAJ_Lr-hAh1m7bRxzFEEsOWDs6K68Y,6058
scipy/special/xsf/cephes/besselpoly.h,sha256=8MdB7tamsSebW9rpHS0TiVlq_YdkJTP1vDTrUx-i6io,1379
scipy/special/xsf/cephes/trig.h,sha256=vqygJpPKDlTylA29ejgX_cu58g76gzoWwyQvO05gwig,1340
scipy/special/xsf/cephes/chbevl.h,sha256=G6HJhFVbhKkXXBN_ZVborRWGBGO6PNAAQ5-zpOYoXBA,1906
scipy/special/xsf/cephes/igami.h,sha256=B_PW8A2s1trORbnVDzKCtqdzslzWbzDsr9vKWey3pqY,12687
scipy/special/xsf/cephes/i1.h,sha256=WuxVJe6_M91pTmZgWFqqahu3slNwkDuzveUfGJlZUps,4740
scipy/special/xsf/cephes/cbrt.h,sha256=bvmwllJjyMlgTUl9FqFXxhiGCXVan-BcrF3iF_UVEMg,3383
scipy/special/xsf/cephes/ellie.h,sha256=ncKPlvJ2naCIouLawoGsiBlwp7hVNFMGwkLHq9Kljeg,9494
scipy/special/xsf/cephes/k0.h,sha256=ZeaVogEPyw0bGDFs4BFg1CR8I1WtIwqQGEPNv6M7B-w,4864
scipy/special/xsf/cephes/ellik.h,sha256=0b40o6PlvzvUCbGnNJ-97BgE-8ZxLYjK9PuCjsoztzw,7601
scipy/special/xsf/cephes/ellpk.h,sha256=jI3WsxFmDAMsovrVyVkt_1voOsYRL2ZesgjuMKLlTpo,3392
scipy/special/xsf/cephes/j0.h,sha256=93xq6Budd0C4hNipx0maXQ_311NLxJMmVFzJe9jEnQk,6878
scipy/special/xsf/cephes/jv.h,sha256=RpS_SWQlINWAr7vr7zCguo6V5zBt5o9ffBcdWLVKhzA,23130
scipy/special/xsf/cephes/sindg.h,sha256=SHZRnvwVhxjZUWNIjTd-cl4VFmZyZoG76nrUwkfyC9c,5634
scipy/special/xsf/cephes/sici.h,sha256=7i2QVx2ij4ehnMTz4lcs3TeOInl-KPoDoQEetRtoPWI,7325
scipy/special/xsf/cephes/rgamma.h,sha256=zBqYhN1-xWE-Vpn2wvDsiDcGuO5qdIcsBEXCOrakwaU,3058
scipy/special/xsf/cephes/kn.h,sha256=SIUl7ePiFLVbXuTf2AC0VhoJkOPHTVQxkY0U5SCGYX8,6264
scipy/special/xsf/cephes/gamma.h,sha256=1ys_rqGE3dR_30YskFwfd8CpKXfCh7UIbZR3fxOtcPA,12004
scipy/special/xsf/cephes/poch.h,sha256=jmJkxvIEnTcuaWPnmDH6lw5kPuE3AZGN1q7zmOaAL1s,2383
scipy/special/xsf/cephes/ellpe.h,sha256=XTCSsSMw8q1CZv19tAdzStjvZRaZ2ONEJNbccSqTiAk,3061
scipy/special/xsf/cephes/airy.h,sha256=eTMfFrUgTjCEn0l8IiuKwBSDFHd5rZMrcTttNK0Akis,11089
scipy/special/xsf/cephes/hyperg.h,sha256=q7BXWxVRmTwkHlJHqdep4CHWrYUWr1Ixv-as_xSKjBA,10458
scipy/special/xsf/cephes/ndtr.h,sha256=y7RhtmvX0n61_Muy7awljyqTURnwtVLbL4Y3rwz9WCY,6681
scipy/special/xsf/cephes/lanczos.h,sha256=2Wp0n-MWPs2l0MtQ1RVaOvcLsC52zELOYPxYJoZK4OA,5494
scipy/special/xsf/cephes/unity.h,sha256=vnNI6j6kpnkPkJuc-4gIiCOHPjPaz8TuChz7aqUzPKE,5053
scipy/special/xsf/cephes/const.h,sha256=FfK7cYG3W8fCzBTe7M6Y8Ejfd_6OL1kzSswC9KyTNk4,3243
scipy/special/xsf/cephes/hyp2f1.h,sha256=kruh1lao3mygHmwVOfvu-MnFunbwNVdf5fZ9Gq5lydk,19986
scipy/special/tests/test_trig.py,sha256=ZlzoL1qKvw2ZCbIYTNYm6QkeKqYUSeE7kUghELXZwzU,2332
scipy/special/tests/test_pcf.py,sha256=RNjEWZGFS99DOGZkkPJ8HNqLULko8UkX0nEWFYX26NE,664
scipy/special/tests/test_owens_t.py,sha256=zRbiKje7KrYJ25f1ZuIBfiFSyNtK_bnkIW7dRETIqME,1792
scipy/special/tests/test_precompute_gammainc.py,sha256=6XSz0LTbFRT-k0SlnPhYtpzrlxKHaL_CZbPyDhhfT5E,4459
scipy/special/tests/test_basic.py,sha256=r_gC4JqRGW3jKi6LwVlGiuVwz5DEEdUOfm_Sew7uNUU,189822
scipy/special/tests/test_wrightomega.py,sha256=BW8TS_CuDjR7exA4l6ADnKhXwgFWUYaN1UIopMBJUZY,3560
scipy/special/tests/test_sph_harm.py,sha256=VEVx2-Rfm2se-n4YU6kafVI1Yml5eYXy1l_uPCJh5pE,3072
scipy/special/tests/test_logsumexp.py,sha256=HnnL-l7kD_pVrcnHihOuz_-gptrC-M1j00Gqugeyt8s,13157
scipy/special/tests/test_wright_bessel.py,sha256=6WHuXB97skPSsoMgXwRlO7bHydFLnl9iDfctEpZE0uE,7694
scipy/special/tests/test_orthogonal.py,sha256=yzZz0GltDQ2JIBQMUXqq8REx-ZuOlRYRa8HUBey0Tsc,32208
scipy/special/tests/test_round.py,sha256=Zv32kFQrDdOPawfGDeZo1PfBG4UsOyKfd3zjbCWLii0,511
scipy/special/tests/test_precompute_utils.py,sha256=MOvdbLbzjN5Z1JQQgtIyjwjuIMPX4s2bTc_kxaX67wc,1165
scipy/special/tests/test_sici.py,sha256=w4anBf8fiq2fmkwMSz3MX0uy35NLXVqfuW3Fwt2Nqek,1227
scipy/special/tests/test_iv_ratio.py,sha256=6Wa4PDSboT1srHiGUOR78_cTvStWgct31cGkLFvDT5A,10108
scipy/special/tests/test_xsf_cuda.py,sha256=yqSB6_ZkuFwFo__noNhKa4LzmzQEqPkxaQd4C9NEWjU,3393
scipy/special/tests/test_powm1.py,sha256=9hZeiQVKqV63J5oguYXv_vqolpnJX2XRO1JN0ouLWAM,2276
scipy/special/tests/test_log_softmax.py,sha256=JdiC5C1Fm16rNdQHVWRu-FGMVOv24DPWRnguDDd1zEY,3415
scipy/special/tests/test_kolmogorov.py,sha256=-Ika_ORUwxDuaCXATLb489T9lDWoPkJR7r7PNRAE0mE,19280
scipy/special/tests/test_zeta.py,sha256=IEPRUdSX5kerDYPmhLWYkYixmUg1ErqHSprQpfkZTP0,11549
scipy/special/tests/test_bdtr.py,sha256=QwGyt0tnutuou25mS0u2LjRgDTYI6ohM2cbZ-He6Os4,3231
scipy/special/tests/test_extending.py,sha256=7Q8NRxp-QBASTY9y0b8xOcAJmrMKhLaruE_MX7nmJ0M,1184
scipy/special/tests/test_digamma.py,sha256=Bm7Hh_aETx6MTN3Wu7Sijy4rYGR_1haNGsi3xfzrAKM,1382
scipy/special/tests/test_boxcox.py,sha256=KK6Ti9TMWKbVaxPVfycrUnM09Th1J2ARhVnI7t7y098,3114
scipy/special/tests/test_hypergeometric.py,sha256=DUDe1YvIXt4IocGlJuqDO5swZ-QOyR2Etj2rwkF-NqQ,9996
scipy/special/tests/test_faddeeva.py,sha256=YLY3Ylp4u_8zxTGxOb5kxNfXXEW0ld_GP2ceOR2ev_Y,2568
scipy/special/tests/test_loggamma.py,sha256=x6kuJf-bEnn5ECdkDSgvk3An_A-9UxVsZpqa49IwAq8,1992
scipy/special/tests/test_spfun_stats.py,sha256=mKJZ2-kLmVK3ZqX3UlDi9Mx4bRQZ9YoXQW2fxrW2kZs,1997
scipy/special/tests/test_erfinv.py,sha256=fzdEHd6MxfSyzQDO93qndXukG2jWj-XNY2X4BJRIdBI,3059
scipy/special/tests/test_support_alternative_backends.py,sha256=xXl1ImMhcYLsx3s2UF5WIWm5thtNN2Iw3kaw8qEm7ww,4422
scipy/special/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/special/tests/test_mpmath.py,sha256=_3scYBHF0sVgGMYV9YP-bT__2EiTxTWnhqROkhk9dus,73771
scipy/special/tests/test_gamma.py,sha256=hb-ZlA2ZNz6gUGvVtMBgXFl_w30HPmthuUEAmNcz0sw,258
scipy/special/tests/test_sf_error.py,sha256=3nOa9ffbrVz2CxfMsHzGVOPaKW_LMV6LDKGfnjjsYXI,4204
scipy/special/tests/test_cython_special.py,sha256=Y79hvQdFnT3w62Lhg8lFDN34hRpDf7vfV3DyNoCqNEY,19128
scipy/special/tests/test_cdft_asymptotic.py,sha256=DBVVLaduZUHSWlKJ5aBXmxgdNm_YjLvWgyiTTcQq04c,1441
scipy/special/tests/test_ufunc_signatures.py,sha256=5tsAbc-QwVe_7YbjbjjYNM1Phiwf51YYqqRx0Hk9EmE,1838
scipy/special/tests/test_logit.py,sha256=8tkUtuoxbu42WZ2LWMrHA2aW_IuB3M0Iqe9FZ0VrJbI,6503
scipy/special/tests/test_spence.py,sha256=fChPw7xncNCTPMUGb0C8BC-lDKHWoEXSz8Rb4Wv8vNo,1099
scipy/special/tests/test_orthogonal_eval.py,sha256=OPW5OeQWVFHyY7SMG2tY8Ar85StXyz0zfsZe9y9ne14,9571
scipy/special/tests/test_cosine_distr.py,sha256=zL7aWLisIEy1oNKjcynqncgsCxcPKvPb9Odr-J5Xa1M,2690
scipy/special/tests/test_pdtr.py,sha256=VmupC2ezUR3p5tgZx0rqXEHAtzsikBW2YgaIxuGwO5A,1284
scipy/special/tests/test_nan_inputs.py,sha256=D85KHG-2K7dqWZDZcxY4n1JvhIxRlQcuCfVbeLogaFs,1858
scipy/special/tests/test_legendre.py,sha256=ndelP3mnTsONEs2TBKC_y1SBK9oCnYV2o8fTgRslFwU,57925
scipy/special/tests/test_exponential_integrals.py,sha256=hlzNhZEXjo5ioPteG0P85qXuMmVD-WVc67e049tvY8Q,3687
scipy/special/tests/test_hyp2f1.py,sha256=r-PJvlIkf27Yo3giszrwwHIo6FWFO9D0jsSXifFuK3w,92259
scipy/special/tests/test_spherical_bessel.py,sha256=yvwnfjt-eCOChCOi48LsPOEhxCLppo1fA8Qcnp8Hzcg,15027
scipy/special/tests/test_precompute_expn_asy.py,sha256=bCQikPkWbxVUeimvo79ToVPgwaudzxGC7Av-hPBgIU4,583
scipy/special/tests/test_data.py,sha256=n6p4MFRXEejYCe_b0Q7CfIu3OXng4jn1nHnMPT9gCOA,30180
scipy/special/tests/test_ndtr.py,sha256=-UMxTIi4CaaLoJ5-SGW9THChPIM3e1_fTY0L877ioNA,2680
scipy/special/tests/test_cephes_intp_cast.py,sha256=yllVoacRDDS_mH7E_pvDux_Jpf7_Fdt3F9Jsgj3_BaY,1129
scipy/special/tests/test_cdflib.py,sha256=wt3axXOqxSwgNYWMAPgQvXlzQIKbWV6kkKal57PobuY,23524
scipy/special/tests/test_ellip_harm.py,sha256=0Kooy3pTFwWqmDT33sjxQZ1S8qjNe-MqO4gJhgcPrrI,9635
scipy/special/tests/test_lambertw.py,sha256=vd5G_70CQz3N_U15mcyE0-2KZ_8QYLKmrJ4ZL-RwFXY,4560
scipy/special/tests/test_boost_ufuncs.py,sha256=I2miMp5IxgexHS6xsHyp9F0YozKr9mpWTpNCq0KI0CY,2245
scipy/special/tests/test_gammainc.py,sha256=Avv52EDQ7M8kUpiVU1BVsW_Gj5HDCzAOojLtoFojKbw,3815
scipy/special/tests/test_specfun.py,sha256=q2JYEnqmUq78rO8no9hXQZ3fc3RuxPrRCcpsLruovDg,1687
scipy/special/tests/test_dd.py,sha256=I7xSqxTD-GYaO0ol25ZjsGZgqCVt13vbcQlUN7teeG4,1564
scipy/special/tests/test_ndtri_exp.py,sha256=13eabgdbfcL37RReiUH7g9amT9XMsTLOfwxFJXR_2Ww,3708
scipy/special/tests/_cython_examples/meson.build,sha256=pTPPwQXCFOd1qe3HpOXcT6lx3HjyUihzu9wTXJqVsnY,527
scipy/special/tests/_cython_examples/extending.pyx,sha256=0ISFhXHFnwuWXg5m9VIYdWGjP_W7hxUE8SwFNkvAM_s,292
scipy/special/tests/data/gsl.npz,sha256=y_Gv3SeZmAanECeZEKLrL59_VZAzx-y3lt6qEMRP6zE,51433
scipy/special/tests/data/boost.npz,sha256=V7XCtn7gHHQVNqrmrZ-PEoEGt_3_FSr889j3dLBkWEQ,1270643
scipy/special/tests/data/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/special/tests/data/local.npz,sha256=bCnljOgnCE-E258bupYEWmHOafHT6j18gop5wTPPiPI,203438
scipy/.dylibs/libgfortran.5.dylib,sha256=SE8YBapNIrJwlKAsh8Jye69X48oL5mSAGMN9h2kJCe0,1902144
scipy/.dylibs/libquadmath.0.dylib,sha256=yxBxqtZk6GagR5DyGxft__59kC9DFRVn4Lj53-hdMfA,364064
scipy/.dylibs/libgcc_s.1.1.dylib,sha256=7MVgvwCegi0V22JpETeCBl27cWR-0e-gxHYp4-Ude4Y,183168
scipy/differentiate/__init__.py,sha256=nZ3imDWtf1QzImE-xsrYHE4kuOa8tEuc99Hl0zAFqzI,621
scipy/differentiate/_differentiate.py,sha256=zFkAn71YqLGg4rDufjlxFzhnXnHMuLuJCmIwNVQ1GG0,50595
scipy/differentiate/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/differentiate/tests/test_differentiate.py,sha256=X8kfzIwvk4GFYV5nL1h86a9HJ79SgU8eDJBxiy3HUKg,28039
scipy/fftpack/_helper.py,sha256=8r6Hh2FA5qTzYyn8y4jfaG41FXMfqQyK6SN8x1dIbaE,3348
scipy/fftpack/__init__.py,sha256=rLCBFC5Dx5ij_wmL7ChiGmScYlgu0mhaWtrJaz_rBt0,3155
scipy/fftpack/pseudo_diffs.py,sha256=h0vkjsSqAThy7OdTkYWVxQqZ3rILohg7MXJqf5CGMTE,658
scipy/fftpack/convolve.cpython-313-darwin.so,sha256=CvsBajGVM5ujj-O9O5bU6Cx-M1G4mWAXQIDTRD8XyzM,225632
scipy/fftpack/realtransforms.py,sha256=9-mR-VV3W14oTaD6pB5-RIDV3vkTBQmGCcxfbA8GYH0,595
scipy/fftpack/basic.py,sha256=i2CMMS__L3UtFFqe57E0cs7AZ4U6VO-Ted1KhU7_wNc,577
scipy/fftpack/helper.py,sha256=M7jTN4gQIRWpkArQR13bI7WN6WcW-AabxKgrOHRvfeQ,580
scipy/fftpack/_realtransforms.py,sha256=2k91B3tSnFm6gKsQn-hRGx4J238CKvqwvQevKgDMuaQ,19222
scipy/fftpack/_pseudo_diffs.py,sha256=T39Owz8EgL4oqmViBT0ggen9DXOtNHWRxh-n6I7pLyw,15936
scipy/fftpack/_basic.py,sha256=Sk_gfswmWKb3za6wrU_mIrRVBl69qjzAu9ltznbDCKs,13098
scipy/fftpack/tests/fftw_longdouble_ref.npz,sha256=pAbL1NrQTQxZ3Tj1RBb7SUJMgiKcGgdLakTsDN4gAOM,296072
scipy/fftpack/tests/test_basic.py,sha256=7nJo-X2q7SHXAMha6WJZUZiufODTiVR8TnT3E8Oq7t4,30554
scipy/fftpack/tests/test_helper.py,sha256=8JaPSJOwsk5XXOf1zFahJ_ktUTfNGSk2-k3R6e420XI,1675
scipy/fftpack/tests/fftw_single_ref.npz,sha256=J2qRQTGOb8NuSrb_VKYbZAVO-ISbZg8XNZ5fVBtDxSY,95144
scipy/fftpack/tests/test_real_transforms.py,sha256=QgaxzmzF5FdUkt5iCtNq-tT5lDjTE_Tyz-BOG5s7RFM,24485
scipy/fftpack/tests/fftw_double_ref.npz,sha256=pgxklBW2RSI5JNg0LMxcCXgByGkBKHo2nlP8kln17E4,162120
scipy/fftpack/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/fftpack/tests/test.npz,sha256=Nt6ASiLY_eoFRZDOSd3zyFmDi32JGTxWs7y2YMv0N5c,11968
scipy/fftpack/tests/test_import.py,sha256=dzyXQHtsdW2WL5ruVp_-MsqSQd_n-tuyq22okrzXlGw,1156
scipy/fftpack/tests/test_pseudo_diffs.py,sha256=ZJU6AkkH6jKjebu_-Ant-dT6tUGwo1Jx9c5kou1floU,13733
scipy/interpolate/interpnd.py,sha256=FDGYwstwT7H3KxD0YcQdbRLti8QkuuMlT7MUdgYRixQ,683
scipy/interpolate/_rgi.py,sha256=M1RJ3ftZ4xfM3teo_UWt-ga7gn47yfJNm4BWmmqNqBU,31001
scipy/interpolate/_rbfinterp.py,sha256=bzuAuZpojP-cKCukD3jVekbQzZfHnrUT13Sex5pkKOI,19723
scipy/interpolate/_ppoly.cpython-313-darwin.so,sha256=JKeVSYKtJwLnSTFmIPWhvwZ8ZQ2BUGwnGA8ljQIgSiM,347152
scipy/interpolate/_ndgriddata.py,sha256=AZk11XftWehCBhiQv7WRqUV0sLS5ltU1IUbOuHRJJN8,12093
scipy/interpolate/_bspl.cpython-313-darwin.so,sha256=A2spF9U4h0Q4lW2Wdgj-XpP3ozvvzr7jjNka5N4UuRc,268616
scipy/interpolate/_polyint.py,sha256=jnfDD6IpNvu2OeL4x7bVL1icdKNW1-EPKLDTdTBbHwA,36366
scipy/interpolate/_cubic.py,sha256=boYHRQjLhs9PlIR5WOFoky8MoH2xEwNUcIHxK3t9J-Q,37727
scipy/interpolate/_rbf.py,sha256=tBeBsMEe_NO1yxEv8PsX8ngVearEn1VfOyrCqEfr_Uc,11674
scipy/interpolate/_dierckx.cpython-313-darwin.so,sha256=Zsn5B09Jpz75WgVbFGoCKu_D_RwGyHRkh3L1XZGv9pk,73952
scipy/interpolate/polyint.py,sha256=ek1EtbIbLLwehb8XDSKeNvIdjTfDQoQ9CSu4TbY8Vbo,672
scipy/interpolate/_ndbspline.py,sha256=RdwKfjW87UC_oJASnDcbiiFl22DJo3Z9y1zRlMFqzVc,14900
scipy/interpolate/ndgriddata.py,sha256=VbvvoDPdWmrk8871y5olPS9StX0S_B27j_oGMAyj8QQ,636
scipy/interpolate/_dfitpack.cpython-313-darwin.so,sha256=2xQz1N28intDt9TSfo2uXiJemtcQhnjhWRKRBkucG30,312336
scipy/interpolate/_bsplines.py,sha256=iISJYDQooeLPFKTyuqtW_RMPPqxNmBFQMdoDvUEYLd0,82693
scipy/interpolate/__init__.py,sha256=QlL_nJvEkGbheWI4k2AgPf_FZ9QQdwKv807y1eFiLp4,3817
scipy/interpolate/dfitpack.py,sha256=z3AS0QKeTqVA-yV2RpSdmYAhL5g5sKud3c-0BcXLexA,915
scipy/interpolate/fitpack.py,sha256=aCH6A3dRouuXW47tK5lEdd2pJa39LCkewY-1zTlI8Hc,702
scipy/interpolate/rbf.py,sha256=6oBxdpsKY8bH36nQnRNiLB9C1bNri8b2PHz9IsUIr-w,519
scipy/interpolate/_rbfinterp_pythran.cpython-313-darwin.so,sha256=lxp4M279iI_PfswUSobAe9q542CYoEOO2cOVqOUloNs,303032
scipy/interpolate/_bary_rational.py,sha256=0iyVrHJy5GDXpIw7cn5TjE78xnAFsbImKOSReqD4zcg,27865
scipy/interpolate/_pade.py,sha256=OBorKWc3vCSGlsWrajoF1_7WeNd9QtdbX0wOHLdRI2A,1827
scipy/interpolate/_interpolate.py,sha256=Yqk9e3zK42-2emJcWDRzTC80tErXnyMkPkKyAs4-TYY,79656
scipy/interpolate/interpolate.py,sha256=Aiu_dJ_oxq-Y1VXns5N5u5K1Wng2hzCgRgRiDhTAiVI,754
scipy/interpolate/_rgi_cython.cpython-313-darwin.so,sha256=-l6LX4WqaMj9dPFZTznAoaYhf5XbiKcx2sofCvKdh7U,227840
scipy/interpolate/_fitpack2.py,sha256=DS3mjEptn2DJEqQ3NQ5WZUZWNYMLdCK_YBffwDUo5dQ,89728
scipy/interpolate/_fitpack_impl.py,sha256=hSnz9q_sibFKhgPlrhlb4a0VvanoIh8sWJjxYooibmY,28678
scipy/interpolate/_fitpack.cpython-313-darwin.so,sha256=zEJeG2QcBT4sGpAxeYcZSjqR1T_98nFUQeO-cjJiUPM,121088
scipy/interpolate/_fitpack_repro.py,sha256=RWdm7I9LBGm5_CBWcgJZYD7MXhppnrj0GZx4-6IAAcI,36710
scipy/interpolate/_interpnd.cpython-313-darwin.so,sha256=76vzkyypy-lMU3oXSwwr8dFvIvoOuOOa3LkoEPA45aE,334848
scipy/interpolate/_fitpack_py.py,sha256=sCzWA-X8ulb0bn-YcaBq9Zo1fpHD0nAoKmURIMbqGek,32157
scipy/interpolate/fitpack2.py,sha256=P15_3gM5eZQYb_-K3c70xKdeIGM81u5WAkVhY8ei4N0,817
scipy/interpolate/tests/test_bsplines.py,sha256=VGX9nui-lJUWHP_jXJd3_4OlqFj_BkA4-xHEXxD_KpU,128301
scipy/interpolate/tests/test_pade.py,sha256=5gmdgTBoJGsY-d813it9JP5Uh8Wc88dz3vPQ2pRZdNk,3868
scipy/interpolate/tests/test_gil.py,sha256=BPC_Ig9lRg28mVHIqdSqWnwBKLukTXFkbrdqUYuskq4,1831
scipy/interpolate/tests/test_fitpack.py,sha256=cFJmwsWhdysO-BEpZ5pMHo6sXSGO1TYWWg_12omcvvk,16589
scipy/interpolate/tests/test_interpnd.py,sha256=IF5nWlRte8ZSPY0Y8eMGya7fKxPQYuoN4OCseGfyens,15545
scipy/interpolate/tests/test_bary_rational.py,sha256=z8_gaM6Ia2GI291aBeOUSsmU9eg8kJ-_HzhXAmtnwpI,15448
scipy/interpolate/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/interpolate/tests/test_polyint.py,sha256=wUZqVdoSRbXm_n7rfcLQ3C_dGCkPxEG-MdpjmBPR7vQ,37296
scipy/interpolate/tests/test_ndgriddata.py,sha256=b_AMpiIj3mlslZXHMnwOqDdI6ORXnO4McbpjGh51dL0,11025
scipy/interpolate/tests/test_rgi.py,sha256=83PyPkDNhE-2Bb42pfpi8yTpjwRmnBuDdYRAp2INXfY,46277
scipy/interpolate/tests/test_fitpack2.py,sha256=jtk_OvC11z9Pifp5cngWRrkauFzRKS2liMGxAt6sjiQ,59819
scipy/interpolate/tests/test_rbf.py,sha256=eoFUrp861RWX4SDbe6VJfDd9_vh9a-f6xwoOrfn7JtA,7021
scipy/interpolate/tests/test_rbfinterp.py,sha256=Sk_e-H18y97dZ1dgCjMxr9bywAUseLBbou7PwlWQ16k,19094
scipy/interpolate/tests/test_interpolate.py,sha256=ZoAyhXV6TAFChCN9wSxe3clAmmm6hXyK_BP5OQggjFc,97777
scipy/interpolate/tests/data/estimate_gradients_hang.npy,sha256=QGwQhXQX_16pjYzSiUXJ0OT1wk-SpIrQ6Pq5Vb8kd_E,35680
scipy/interpolate/tests/data/gcvspl.npz,sha256=A86BVabLoMG_CiRBoQwigZH5Ft7DbLggcjQpgRKWu6g,3138
scipy/interpolate/tests/data/bug-1310.npz,sha256=jWgDwLOY8nBMI28dG56OXt4GvRZaCrsPIoKBq71FWuk,2648
scipy/fft/_realtransforms_backend.py,sha256=u4y4nBGCxpTLVqxK1J7xV6tcpeC3-8iiSEXLOcRM9wI,2389
scipy/fft/_backend.py,sha256=5rBxK8GQtCMnuPHc-lNQdpH4uFFZ9_5vBukkDv6jRRA,6544
scipy/fft/_helper.py,sha256=wQ5ZlvOEY9snn32Yg6p0W_DcQu70JRaHTu_lrrODtlA,12385
scipy/fft/__init__.py,sha256=0cjHIwyHnjoz1XUUe3OB70vrQR0-pFp8Uv34-U-FGRg,3632
scipy/fft/_fftlog_backend.py,sha256=UgoePwhoMoLxvQ5soSUZkVWvWWTP7y1xWVAD9BlrdJY,5304
scipy/fft/_debug_backends.py,sha256=RlvyunZNqaDDsI3-I6QH6GSBz_faT6EN4OONWsvMtR8,598
scipy/fft/_realtransforms.py,sha256=QmO9CDqrAsvBcLNgIzFBIWBTYsSUCRJ_Cj1myv73KlE,25386
scipy/fft/_basic_backend.py,sha256=Qms-BE7DCJYNSq9Vd5utnKiwVTqRIUzLYYEiMyTdpfE,7447
scipy/fft/_fftlog.py,sha256=JeLVCAgfB99brT2Ez9tzdapmhWrTfYCUYEi2KTvPzIQ,7864
scipy/fft/_basic.py,sha256=lGJ8qQTMXUJEbq_2vwfPPPlX7b4j358ks9LLretOtEY,62997
scipy/fft/tests/test_backend.py,sha256=DFJ6OKV6gRw4p9OuVfy1ENTeLJCbYS2GuppwpJnwQGQ,4285
scipy/fft/tests/test_basic.py,sha256=h0JPW3pX0da5F5zMaxodnGMZtmxhmQto14LkRUZ-UXI,20719
scipy/fft/tests/test_multithreading.py,sha256=JMSXQocScFghpsy47zov03R5MbEY0Z3ROGt6GxFeWzo,2150
scipy/fft/tests/test_helper.py,sha256=TZChUViGsjAWHn21OmotAQyJ4C_icojkSSnG3cO_2Uc,20187
scipy/fft/tests/mock_backend.py,sha256=p17Hfg6xuoF6Ldxwe1PZ-79Lf_r9FyJUR00N4TokM8k,2685
scipy/fft/tests/test_real_transforms.py,sha256=0lSYAeDXOft_wvKGlI37rIAB1OXfxl-wZVf-Grxy6yU,9287
scipy/fft/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/fft/tests/test_fftlog.py,sha256=D98q61cNJx2UsQyJ-jHwGhYha_HOf0KxJqOZsljHWH8,7362
scipy/fft/_pocketfft/LICENSE.md,sha256=wlSytf0wrjyJ02ugYXMFY7l2D8oE8bdGobLDFX2ix4k,1498
scipy/fft/_pocketfft/__init__.py,sha256=dROVDi9kRvkbSdynd3L09tp9_exzQ4QqG3xnNx78JeU,207
scipy/fft/_pocketfft/realtransforms.py,sha256=4TmqAkCDQK3gs1ddxXY4rOrVfvQqO8NyVtOzziUGw6E,3344
scipy/fft/_pocketfft/basic.py,sha256=4HR-eRDb6j4YR4sqKnTikFmG0tnUIXxa0uImnB6_JVs,8138
scipy/fft/_pocketfft/helper.py,sha256=mmiRCzeNuPSUUFYubG1VRO4nMIRDDelSGDZrdomBno0,5841
scipy/fft/_pocketfft/pypocketfft.cpython-313-darwin.so,sha256=rcq35CDtNXS8wW96MIctW98X83JpWKtKT0cxZWC8vi0,900800
scipy/fft/_pocketfft/tests/test_basic.py,sha256=wG06l401F3jGl_2mzwdTU1-7X-tp54fYcMqAqId2dUw,35715
scipy/fft/_pocketfft/tests/test_real_transforms.py,sha256=vsQ3RdHDtJKhypf4v1MLTgy782XWvFMykPHrDie0bio,16879
scipy/fft/_pocketfft/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/_base.py,sha256=zVk_n3nwri0SW2dC4SPiq2TXE9EstsFZsIkTpV4uQeU,48805
scipy/sparse/csc.py,sha256=EV_LxYjPiRsTV6-J8kUefNna-R0tdI5uBt9Fj_XWlwc,609
scipy/sparse/_data.py,sha256=NpxPIjJbmJDM_3AbRndYN55ffhz4j2aYV2ABgL3yM0c,20488
scipy/sparse/_sparsetools.cpython-313-darwin.so,sha256=p_EEfcTTngO69gBudZ1v_hylh2PVRIIwLe8RNesgfAs,3563040
scipy/sparse/_compressed.py,sha256=7fPtGYHLQ8hWbrSqqsaEXAf4DdK9IhBJibI6_ouE-gU,58863
scipy/sparse/sparsetools.py,sha256=pCcuyQYvIahrvr43V398XHyqwcGtWCPLFH6n1uSYmB8,516
scipy/sparse/dok.py,sha256=LMnaLFd266EZ3p4D1ZgOICGRZkY6s7YM0Wvlr6ylRn0,733
scipy/sparse/coo.py,sha256=VRF6kaYsVtyprwYrEuy1gRcCU5G7xsKyY0L1zJ_9JiQ,844
scipy/sparse/_bsr.py,sha256=7qZwcg8KeP-E-zYJn8uTcd9UqjP2NyyQ0CaqPcieWQA,30934
scipy/sparse/compressed.py,sha256=rbaz4AoTJvNnfnwEx4ocDXlkHJPOxe9DzqxCcJoHY2g,1009
scipy/sparse/dia.py,sha256=0y5_QfvVeU5doVbngvf8G36qVGU-FlnUxRChQ43e1aU,689
scipy/sparse/_index.py,sha256=Mu4nOO8s0bq0O0l7NXUBuNMhdaal9tXYcxlRzqotYb0,16376
scipy/sparse/_sputils.py,sha256=xTe_MUII85GErqsA-DbOMdUQ1UFuOWxyyWB82xS_rBg,20750
scipy/sparse/spfuncs.py,sha256=TWpfkZk3JErNajVFUH5B85d3r6UuSv0Rsx0lMtUSac0,508
scipy/sparse/__init__.py,sha256=OShVd94qpqQr4HMNPAvbMRQKf0Z6cL7bfRSbxcx99YQ,9361
scipy/sparse/lil.py,sha256=Gve3XHYPYZavcUPJz1TSOhjv6AtPpkKBHTzCK6FG8ek,562
scipy/sparse/sputils.py,sha256=PsqT7RUmiO8ph5jG8GHYmPbacDQFljjc0SL7RMxweJU,508
scipy/sparse/_spfuncs.py,sha256=lDVTp6CiQIuMxTfSzOi3-k6p97ayXJxdKPTf7j_4GWc,1987
scipy/sparse/_csr.py,sha256=HbHai24yw-JPg9PZrgcFLEdfqQfj1BjmvNF_01qj-os,18156
scipy/sparse/bsr.py,sha256=CsYirxoLqHwBiEyNbOgGdZMx4Lt3adKZ-7uVv1gpzCY,811
scipy/sparse/_coo.py,sha256=Oiyq04Pe0CPnEvYK-6Mtdo7XuQT8b1mkL7dx6Mze3To,64224
scipy/sparse/_csparsetools.cpython-313-darwin.so,sha256=F4yFdnAd5QFmCz6zr4Aj_ymYoIu9WV38VVU5f-__9Wo,531968
scipy/sparse/_dia.py,sha256=2L51l7l9BKKVGtLvHvYz3u0RPJOcXhZ2L0-aV1FQaFM,20067
scipy/sparse/_lil.py,sha256=uS3i5M_yhLjTDk9xySG_4COGgJA2QcwIpKphuwhcCV4,21125
scipy/sparse/_matrix_io.py,sha256=0ZEoczSQq59zOGd_eWk6sfACt62vdQmth3ia7uqWFTM,5960
scipy/sparse/csr.py,sha256=9UrWUoq5-hSl9bcaVeWxN4tmPJisTQ_6JiISCyrlMCw,658
scipy/sparse/construct.py,sha256=i9lHBSRsDkvoNCbF9b7mZ0C2fHCjKU5CKCE30c-CxMc,925
scipy/sparse/_csc.py,sha256=KKVzIuWFCRlWGNCQMZpZp-_es0RefHimb-DW2AhNj6U,11142
scipy/sparse/extract.py,sha256=6qT2PNOilsEhDWl6MhmgpveIuQr4QCs3LATwIrBroOQ,567
scipy/sparse/base.py,sha256=8Yx-QLKSRu9LJjgG-y8VqsRnsjImB2iKoJFxTgKGFsI,791
scipy/sparse/_construct.py,sha256=d044HGf_0-UqzsmifsAKCw2bPbQLTD1-vIFJOhxbTks,47960
scipy/sparse/_matrix.py,sha256=-iZoYGC2dchFI3QKhmOpOCZgousk6vTO95jKgNDorg4,4427
scipy/sparse/data.py,sha256=qGDAuAvTASgQ7wXXZ9t2JPp0rNBNVxObTTzXNHDRSEo,573
scipy/sparse/_extract.py,sha256=0NWW00hxjk5gl4CjNRHtvcqsx54yNei2VVbqARMOlAo,5058
scipy/sparse/_dok.py,sha256=tbmVoRu0-ECKB12hXW61qU82-kA6rcQhYQRJ3zzqoU4,23011
scipy/sparse/linalg/isolve.py,sha256=diSAxpbYg8PeH75QOEE-CREO8p39f4BZK2dGynJDKIc,649
scipy/sparse/linalg/_interface.py,sha256=i59d_y4vcKyIRXBmqbjj0YUY30CNo27dQvkEZC9Q2UQ,29420
scipy/sparse/linalg/matfuncs.py,sha256=H2qJl4ZZqZ4bI-E9NCbu1oFfto0EdFxCTKTugMPHRHg,570
scipy/sparse/linalg/interface.py,sha256=_KXBkGhZWvY_ZmGixqWMZe6J64bCPdjtrqr63HvicUI,573
scipy/sparse/linalg/_expm_multiply.py,sha256=KOSuV2qF4OSKrLGSwUAFT1ibnv4bhU9JBFJkvy9AVXY,26491
scipy/sparse/linalg/__init__.py,sha256=KL54k4eDwEf7mHbL21uZe87S2rnSPIFcEI-pT3UpLIw,4111
scipy/sparse/linalg/_matfuncs.py,sha256=JaiiIwtP6Uzk6Jal8D9Ep9jTCxSyJZIdKamfzJN8wlA,29338
scipy/sparse/linalg/_svdp.py,sha256=dUr5v53cR5S40r71QCAVy0qUdKMxOviaWAT0ptrcjTQ,11200
scipy/sparse/linalg/_norm.py,sha256=MizhY4JL8pqcuP2suUlP1hMkwL1fIoyYHkiaEKuKqTQ,6163
scipy/sparse/linalg/dsolve.py,sha256=fvCzVUda-h-WzwGWDss4FVuv6TVE-OKHzARBlUCDIJw,654
scipy/sparse/linalg/_special_sparse_arrays.py,sha256=e7Y4OOurKa3eMyOnWaN-e6YQOM17onTESURjDpWUYS4,34225
scipy/sparse/linalg/_onenormest.py,sha256=BkWu89ffmifkBdLH--IQ7DiW0hvDkVEiudUx4HRVmcI,15480
scipy/sparse/linalg/eigen.py,sha256=4BTo8Tc9SNQaruyrF4gRdFE5NstiA0XH9I44IyikZQ4,626
scipy/sparse/linalg/_eigen/_svds.py,sha256=niV8PR0Aonw85rbiSPpL-RswAn9TltpUwcni3Qu_kl8,19908
scipy/sparse/linalg/_eigen/_svds_doc.py,sha256=0_sC8kKbu3b5BYpGl16sPLrZu6mDxiFhj8xkbG2w5-U,15003
scipy/sparse/linalg/_eigen/__init__.py,sha256=SwNho3iWZu_lJvcdSomA5cQdcDU8gocKbmRnm6Bf9-0,460
scipy/sparse/linalg/_eigen/arpack/_arpack.cpython-313-darwin.so,sha256=iP18ZLEJP-6ovb-s5m2Ya2PqL3TmQt7WzrsS3CAJJT8,480784
scipy/sparse/linalg/_eigen/arpack/__init__.py,sha256=zDxf9LokyPitn3_0d-PUXoBCh6tWK0eUSvsAj6nkXI0,562
scipy/sparse/linalg/_eigen/arpack/COPYING,sha256=CSZWb59AYXjRIU-Mx5bhZrEhPdfAXgxbRhqLisnlC74,1892
scipy/sparse/linalg/_eigen/arpack/arpack.py,sha256=hajyf3Ri9i9GHrqsRMFiDkfQL4jB5ZYQTNLVT7ZY9CY,67169
scipy/sparse/linalg/_eigen/arpack/tests/test_arpack.py,sha256=yiL2zpB7ti0rwEP5DYXRZD-7JE3m6Wer07MJ4O65e5s,23735
scipy/sparse/linalg/_eigen/arpack/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_eigen/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_eigen/tests/test_svds.py,sha256=3rQz_qRbkEpu9tFNK98MfRDYMDVv5ZyPaALTzWhBW54,36794
scipy/sparse/linalg/_eigen/lobpcg/lobpcg.py,sha256=vMsZlXCgKzn8l0PzHQFFadrAGfG9Fp0aTxwihATTqKM,41951
scipy/sparse/linalg/_eigen/lobpcg/__init__.py,sha256=E5JEPRoVz-TaLrj_rPm5LP3jCwei4XD-RxbcxYwf5lM,420
scipy/sparse/linalg/_eigen/lobpcg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_eigen/lobpcg/tests/test_lobpcg.py,sha256=15uXmcxi0BwPYtuD5kaoddsLE9-bN7QvHJimqFGmtOE,27421
scipy/sparse/linalg/_propack/_dpropack.cpython-313-darwin.so,sha256=ewXb5eTwPPobhL52i9x_xzMf8acN_9Yw78I9lM3lPG0,161328
scipy/sparse/linalg/_propack/_spropack.cpython-313-darwin.so,sha256=IBWlNDOZPwahHua2mlr2T6vZAWiM6PUEwoif6wwix1w,161328
scipy/sparse/linalg/_propack/_cpropack.cpython-313-darwin.so,sha256=pL0RDFS6KGEdo0VdD2r8TUgP7647qkDuPZDkpWMf_PY,179344
scipy/sparse/linalg/_propack/_zpropack.cpython-313-darwin.so,sha256=GgpjskIlXd4beEMfFl1dh69h1EQaxtfj10bThqpfQYQ,179376
scipy/sparse/linalg/tests/test_matfuncs.py,sha256=TqDnJFHiKdiwXP0Gb6yaXNAeiReV6TdBe4wMQXmXTI4,21740
scipy/sparse/linalg/tests/test_onenormest.py,sha256=Tzn0FcVcKmbjYoseUkkxjq4mCOhG2cPfDyo9fQCYVPI,9252
scipy/sparse/linalg/tests/test_norm.py,sha256=dJp4VNGpnL5xET60-b1epJqIBZ4g-zDALZWS5Wg60cQ,6716
scipy/sparse/linalg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/tests/test_expm_multiply.py,sha256=K7tSwySHF0sMxq06391fhzBwn-eRskwVn74QussqerE,14845
scipy/sparse/linalg/tests/test_special_sparse_arrays.py,sha256=2Z7r1LPx7QTekuXNTLcspGOdJ9riRwioGIpxzIa0Kh4,12854
scipy/sparse/linalg/tests/test_pydata_sparse.py,sha256=vXYRhevEwvMSmdwwn1hJfAuaKuS-taRx0BpcUzI6t8g,6809
scipy/sparse/linalg/tests/test_propack.py,sha256=--SIFSXDGzyBOTdGwOhgrYhSkbVy1RiyL_Dt_Yonp_4,5567
scipy/sparse/linalg/tests/test_interface.py,sha256=q5rZwUzJBIwiW__n-IzztR6HkZEkv8oePzfG0f1j8K8,21086
scipy/sparse/linalg/tests/propack_test_data.npz,sha256=v-NNmpI1Pgj0APODcTblU6jpHUQRhpE9ObWb-KYnu6M,600350
scipy/sparse/linalg/_isolve/lsqr.py,sha256=Ca2SjyNwMFXSckUTW_LqYFkFc5CWOaZ1yiYB0tK2uB8,21322
scipy/sparse/linalg/_isolve/__init__.py,sha256=Z_eQUYbe6RWMSNi09T9TfPEWm8RsVxcIKYAlihM-U-c,479
scipy/sparse/linalg/_isolve/tfqmr.py,sha256=_Uyy3skUaIHpqBD18H-poX8Tot1IfqkMmnF6h0iU6TY,6240
scipy/sparse/linalg/_isolve/iterative.py,sha256=Vhk3_ozYf8Pscte_Vkl_u9AAlFyJxVNpe8jAqviHlF4,33861
scipy/sparse/linalg/_isolve/utils.py,sha256=I-Fjco_b83YKUtZPVdobTjPyY41-2SHruVvKZVOIXaU,3598
scipy/sparse/linalg/_isolve/_gcrotmk.py,sha256=du3X8-NokWITHFi6DbzYnMO4uba5uHSqWIZ4Zm_9HhA,15788
scipy/sparse/linalg/_isolve/minres.py,sha256=3heKvLLuULWhdCrhbhaanZvu5J6-EbQEtwOIzI6uEFs,10887
scipy/sparse/linalg/_isolve/lsmr.py,sha256=8MRtv-FJa7nOHlJ7MZ4TsQiWAkZwntD0r55SOQuRqvI,15650
scipy/sparse/linalg/_isolve/lgmres.py,sha256=A-mgYLEvzt5n10yMDoo3ZPNweULpp52aVAMhpTrbOe0,8695
scipy/sparse/linalg/_isolve/tests/test_utils.py,sha256=VlmvctRaQtjuYvQuoe2t2ufib74Tua_7qsiVrs3j-p0,265
scipy/sparse/linalg/_isolve/tests/test_lsqr.py,sha256=tYKtlTuXMYYHvfpmrhdCqlzk0BIyohl2b-4b0SA6nBg,3759
scipy/sparse/linalg/_isolve/tests/test_iterative.py,sha256=cDCvcVc_a3aPzDNWKX_3CHUADQ0SpAFeyNsejbQEdE8,26181
scipy/sparse/linalg/_isolve/tests/test_minres.py,sha256=d_rLkqdObBDD4FBpTOYgzwysTqBtYjgV5v1IDLhyr-8,2434
scipy/sparse/linalg/_isolve/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_isolve/tests/test_lgmres.py,sha256=9J0oq4KEg4UkIOwPQnp7z7U9bJMpCV9NslHCDANCccI,7448
scipy/sparse/linalg/_isolve/tests/test_gcrotmk.py,sha256=QiLhe-Z9KRv1TMfe5cbCLO9Nm4vhpNtJEXPChaP_4Lg,5861
scipy/sparse/linalg/_isolve/tests/test_lsmr.py,sha256=6D3aZELcgJrp3Qf_HisAIowcwxnCzAiCfTf77YNsbrg,6362
scipy/sparse/linalg/_dsolve/_add_newdocs.py,sha256=4Nm6RAKQlKI4lQt4z20v0D6m0Vk8eqp0mIzEk5gfztA,3743
scipy/sparse/linalg/_dsolve/linsolve.py,sha256=F-KfpTKnlUl-ZXoDPnQ_2jY9NmsgByAiDsMaPHnHRFg,30697
scipy/sparse/linalg/_dsolve/__init__.py,sha256=PIX7n_d0LOMZZZ65Dz4Mgz9trjKGB2kLaF16PQLkAIs,2039
scipy/sparse/linalg/_dsolve/_superlu.cpython-313-darwin.so,sha256=kOqQCR1Eg91Q65LA2bOoLY_OVQAMYVfoePhMHd_OSow,280752
scipy/sparse/linalg/_dsolve/tests/test_linsolve.py,sha256=CDsPCMpry6XBFOqMcRFUiY6QkkzOdKl7avm6enGrHgc,32893
scipy/sparse/linalg/_dsolve/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/tests/test_construct.py,sha256=AayVXyTauNJPY4SpDLy9WNDs8k30J8aNz3-T05hwYfo,38433
scipy/sparse/tests/test_arithmetic1d.py,sha256=EHAimtdcEPpGpyCluJ8DC-WWbkFwlR3266vEVU1Vdss,11875
scipy/sparse/tests/test_coo.py,sha256=TPcHyD3b3qA37ZU94h5WLM2stFSfZ-8PoVqEZMcQoz8,29134
scipy/sparse/tests/test_common1d.py,sha256=ys6uPq1Uu5dluDBAcqROTOrFbgZVpV1G4MUU1QnEpb4,15504
scipy/sparse/tests/test_matrix_io.py,sha256=sLyFQeZ8QpiSoTM1A735j-LK4K0MV-L7VnWtNaBJhw4,3305
scipy/sparse/tests/test_spfuncs.py,sha256=ECs34sgYYhTBWe4hIkx357obH2lLsnJWkh7TfacjThw,3258
scipy/sparse/tests/test_sputils.py,sha256=fEPvwo6sjwZ9ytdnufFIUE-gEkIe10DbdsX51v3ljyo,15083
scipy/sparse/tests/test_csc.py,sha256=rB2cBXznxPdQbMZpdQyQitUdCdEeO6bWt7tQ_LBGGDw,2958
scipy/sparse/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/tests/test_dok.py,sha256=25jxMgYsQ_q-aN5uDvALRX6PuV83LVktQeEF3gVINm8,5959
scipy/sparse/tests/test_sparsetools.py,sha256=zKeUESux895mYLdhhW_uM5V1c-djdEKnZ-xURx5fNrw,10543
scipy/sparse/tests/test_indexing1d.py,sha256=r6G8k9GNGfMcVgDg13N2kvmaDkl9FL2CzYYfbLfKXQU,20754
scipy/sparse/tests/test_array_api.py,sha256=U8TBj4ZJ5Bc6sOsJ6Q8HgnGBhGJK-sLXS1QD_9pK-4c,14201
scipy/sparse/tests/test_minmax1d.py,sha256=UBeHcN4Pw_VAPXtgsyDev5pK3eXvisiiLjibeaiA8S0,4269
scipy/sparse/tests/test_extract.py,sha256=4qUPrtCv9H7xd-c9Xs51seQCiIlK45n-9ZEVTDuPiv8,1685
scipy/sparse/tests/test_base.py,sha256=GzHpsVclH2ekn1GmboJq771n2akLou8680qLdRhoDZE,213738
scipy/sparse/tests/test_csr.py,sha256=J8q7e22jt0mGv0OdhdRX5xxcAkVWRclHAOmWwWMeauA,7623
scipy/sparse/tests/data/csc_py3.npz,sha256=axuEMVxwd0F-cgUS0IalpiF8KHW4GNJ3BK6bcjfGnf4,851
scipy/sparse/tests/data/csc_py2.npz,sha256=usJ_Gj6x_dEC2uObfdYc6D6C8JY4jjROFChQcZhNAfo,846
scipy/sparse/csgraph/_tools.cpython-313-darwin.so,sha256=f2SKohgoHNVS1Def5A5x58eqOeUY5z8gd9Szz4EIodg,184016
scipy/sparse/csgraph/_flow.cpython-313-darwin.so,sha256=zxqo4EpugFqHKU7gV8loj9Vlk9rvLgteKv71Oj3Yayg,278936
scipy/sparse/csgraph/_laplacian.py,sha256=bpCduRWjIhcDpclvPbftx74PExTiW0P3EE6_Ztiop1Y,18273
scipy/sparse/csgraph/_reordering.cpython-313-darwin.so,sha256=5ja_VNXcQRJ1K6atr2C-PtcpTk6IpKjATGXajxv2OM0,261952
scipy/sparse/csgraph/__init__.py,sha256=znrEd48JFLdlcevl8IFDSM104Yl1YvXC0O_f8OUWATs,7842
scipy/sparse/csgraph/_min_spanning_tree.cpython-313-darwin.so,sha256=7rP7T1UsWJOpsZQwUp_aRRRIhdD1jQ1aKw1YSzb897c,209080
scipy/sparse/csgraph/_validation.py,sha256=SxINtd4jYyH0YWdzspr8JR0syZfO3nMj7C60GWBUr6k,2629
scipy/sparse/csgraph/_matching.cpython-313-darwin.so,sha256=PZa8Q1ccN23h8o9ZoaQ5B-P5Hz1Ux1mfBSeOJZTDGlQ,277072
scipy/sparse/csgraph/_traversal.cpython-313-darwin.so,sha256=QEosBpNTK5l6DxUibEuSDEBHhwIF8NAWKJjoLY17zMM,480096
scipy/sparse/csgraph/_shortest_path.cpython-313-darwin.so,sha256=p6OG9uBpDTy_X2gm6UFMBjcXZ7s0TZKsyrNRGHl-bQo,431176
scipy/sparse/csgraph/tests/test_conversions.py,sha256=3n2UJ_rwdcTkD8NfwDrk-8UBplJkqMFw12yPIwX9-R8,1854
scipy/sparse/csgraph/tests/test_shortest_path.py,sha256=OP4td7B9TLM79zTPQAi5LLLGvW81D1iNuR27HOlZcA8,16575
scipy/sparse/csgraph/tests/test_flow.py,sha256=I7csygtef5f6Uv67t2y3UZsln8Gg4eS1RE5zr7Xm-Eg,7718
scipy/sparse/csgraph/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/csgraph/tests/test_graph_laplacian.py,sha256=9nQDRj5_oVK0CXM-DW2Xb2jofW3YCiI0QBezdBUl_60,10936
scipy/sparse/csgraph/tests/test_pydata_sparse.py,sha256=DThJQ9OwZMvTQnoPKfGZ5sCdXtBWLqfMFFeuHGOuiOs,4869
scipy/sparse/csgraph/tests/test_matching.py,sha256=wX0Pml9DHokv5_ve0L0t6Rse-JsBWU6Jr6LZ1I8HmTE,11871
scipy/sparse/csgraph/tests/test_spanning_tree.py,sha256=q4LYiXxfwWUc1io4vRVBr9uxMacfdefPvcRlb3TOEnw,2164
scipy/sparse/csgraph/tests/test_connected_components.py,sha256=a2HZjm7HsC0STqiDnhN6OJL4yIMcM28VNVtMXDI2BqE,3948
scipy/sparse/csgraph/tests/test_traversal.py,sha256=PD1EJ8XD3xyCWU7SF9-Qw-skhEAI3tiNDxrabsXgU2I,6149
scipy/sparse/csgraph/tests/test_reordering.py,sha256=_WNqdGcU-WNhQRpjCq4Nhp8YY6cmVKb13au5sJPpzig,2569
scipy/spatial/_hausdorff.cpython-313-darwin.so,sha256=ZucRnKSz1IQ1Cba6r3yrAEjn-1dQNzS112Bf_83NcgA,191792
scipy/spatial/_voronoi.cpython-313-darwin.so,sha256=o-xPWu4EPBsizj0Y0X_UBTUVYnGFpA8Xxx7Tz8lkqfU,191280
scipy/spatial/_spherical_voronoi.py,sha256=v1XkbWI7yoXQ6EJmJHs185vl0qHV8yfRrm3c_gBGyzg,13577
scipy/spatial/_voronoi.pyi,sha256=aAOiF4fvHz18hmuSjieKkRItssD443p2_w1ggXOIs1g,126
scipy/spatial/_procrustes.py,sha256=qvhHPHt_OIKo-ge_k19S4VWqbP6ZgMXLVnNey0JxTb8,4427
scipy/spatial/_distance_pybind.cpython-313-darwin.so,sha256=c12fH7PpkFvdtni0NaekvnaReiRH_HpnuydXVnLJxSo,604104
scipy/spatial/__init__.py,sha256=-FVg_WjbK0J0U2kyei6Fz6NgqEso5cipWZ5gHnqjErs,3731
scipy/spatial/_kdtree.py,sha256=ImDiR14DOAhwK--x9VhMjAlH_uhumsKuMin1Np63O7Q,33479
scipy/spatial/distance.py,sha256=h_8YsmV28ycxIm3k9-o3EYeiHBrRc7uoUj5hMg_jC6s,98001
scipy/spatial/_ckdtree.cpython-313-darwin.so,sha256=-o-Kea5pr-rchF43RxGDlr17Lg9Gf6d0rFvhQon78Tc,650752
scipy/spatial/kdtree.py,sha256=ZYJL8A_WpLyEH29aFQGLbxd9ttFdGBgdglbgAfsvhz8,636
scipy/spatial/_qhull.pyi,sha256=dmvze3QcaoA_Be6H8zswajVatOPwtJFIFxoZFE9qR-A,5969
scipy/spatial/_qhull.cpython-313-darwin.so,sha256=WsTeM95_tJZHNIaB-HuWSgm35PrnqMihhhch9vj3_dU,871472
scipy/spatial/_geometric_slerp.py,sha256=d3pavtaMuIIKjupWLwFLt7WrfqvtT18u7wcsBdnuOTs,7951
scipy/spatial/_plotutils.py,sha256=cp94kSvt1QzWV6YWjeTrLh0lbWoVQu_0-iagVpoIgMo,7557
scipy/spatial/ckdtree.py,sha256=0IssUT415ieBOJuvfZJxIra-TeYyd0KxDGLrXDZ_GGw,523
scipy/spatial/_distance_wrap.cpython-313-darwin.so,sha256=fbh2Qt_M6atD7OKDpJlfP3GJdinWC964d09JJFz5UBk,121128
scipy/spatial/qhull.py,sha256=aFE-KscuINt6QIhFC2dqhwFCYu3HSBkVXDH5exHH71s,622
scipy/spatial/distance.pyi,sha256=rVZpbHbTPWeqYN7aBSDBDIt3MLQWbUIYmgwzWJiODjE,5238
scipy/spatial/tests/test__procrustes.py,sha256=wmmnUHRdw_oID0YLi404IEWPH6vEGhvHXSeGPY_idHo,4974
scipy/spatial/tests/test_kdtree.py,sha256=gIkFKF8ek0xuMjhUu9uWJGsQ0GmED4FtqNiasNCKzho,49314
scipy/spatial/tests/test_distance.py,sha256=793ubGYbWj74ICe9khubsDoxzrjE32-HxFJllgXGptU,87892
scipy/spatial/tests/test_slerp.py,sha256=gjBdGVUbaPctmw05Z297dUjq5a1lH3erm1meMQoVzeo,16427
scipy/spatial/tests/test_qhull.py,sha256=ThiPSBGFYEVW3kxfOzz2BSOEijRNbMX1sobYNBZ4g5M,49954
scipy/spatial/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/spatial/tests/test__plotutils.py,sha256=fASbg0i7iLiJIEj5vIkiDuTq3wU0z3mKJY019kzKrFk,3814
scipy/spatial/tests/test_spherical_voronoi.py,sha256=YCVSpO7-RrmKaAivwrLh5rZJ6CTTNKuIJ9iyhXsi178,14500
scipy/spatial/tests/test_hausdorff.py,sha256=XcDEzwFuOR9BaLegIj-DPp5GrAi_RsvcW8oGqJf0xkg,8217
scipy/spatial/tests/data/cdist-X2.txt,sha256=_IJVjXsp3pvd8NNPNTLmVbHOrzl_RiEXz7cb86NfvZ4,11500
scipy/spatial/tests/data/pdist-chebyshev-ml.txt,sha256=NEd2b-DONqUMV9f8gJ2yod17C_5fXGHHZ38PeFsXkyw,3041
scipy/spatial/tests/data/iris.txt,sha256=k19QSfkqhMmByqNMzwWDmM6wf5dt6whdGyfAyUO3AW0,15000
scipy/spatial/tests/data/pdist-correlation-ml-iris.txt,sha256=l2kEAu0Pm3OsFJsQtHf9Qdy5jnnoOu1v3MooBISnjP0,178801
scipy/spatial/tests/data/degenerate_pointset.npz,sha256=BIq8Hd2SS_LU0fIWAVVS7ZQx-emVRvvzgnaO2lh4gXU,22548
scipy/spatial/tests/data/pdist-jensenshannon-ml-iris.txt,sha256=L7STTmlRX-z-YvksmiAxEe1UoTmDnQ_lnAjZH53Szp0,172738
scipy/spatial/tests/data/cdist-X1.txt,sha256=ULnYAgX2_AwOVF-VE7XfnW5S0pzhx7UAoocxSnXMaWs,5750
scipy/spatial/tests/data/pdist-jaccard-ml.txt,sha256=Zb42SoVEnlTj_N_ndnym3_d4RNZWeHm290hTtpp_zO8,3041
scipy/spatial/tests/data/random-int-data.txt,sha256=xTUbCgoT4X8nll3kXu7S9lv-eJzZtwewwm5lFepxkdQ,10266
scipy/spatial/tests/data/pdist-minkowski-5.8-ml-iris.txt,sha256=jz7SGKU8GuJWASH2u428QL9c-G_-8nZvOFSOUlMdCyA,178801
scipy/spatial/tests/data/random-double-data.txt,sha256=GA8hYrHsTBeS864GJf0X6JRTvGlbpM8P8sJairmfnBU,75000
scipy/spatial/tests/data/pdist-jensenshannon-ml.txt,sha256=-sZUikGMWskONojs6fJIMX8VEWpviYYg4u1vipY6Bak,2818
scipy/spatial/tests/data/pdist-minkowski-3.2-ml.txt,sha256=DRgzqxRtvQVzFnpFAjNC9TDNgRtk2ZRkWPyAaeOx3q4,3041
scipy/spatial/tests/data/pdist-spearman-ml.txt,sha256=IrtJmDQliv4lDZ_UUjkZNso3EZyu7pMACxMB-rvHUj0,3041
scipy/spatial/tests/data/pdist-cosine-ml-iris.txt,sha256=hQzzoZrmw9OXAbqkxC8eTFXtJZrbFzMgcWMLbJlOv7U,178801
scipy/spatial/tests/data/pdist-euclidean-ml-iris.txt,sha256=3-UwBM7WZa4aCgmW_ZAdRSq8KYMq2gnkIUqU73Z0OLI,178801
scipy/spatial/tests/data/selfdual-4d-polytope.txt,sha256=rkVhIL1mupGuqDrw1a5QFaODzZkdoaLMbGI_DbLLTzM,480
scipy/spatial/tests/data/pdist-correlation-ml.txt,sha256=S4GY3z-rf_BGuHmsnColMvR8KwYDyE9lqEbYT_a3Qag,3041
scipy/spatial/tests/data/random-uint-data.txt,sha256=8IPpXhwglxzinL5PcK-PEqleZRlNKdx3zCVMoDklyrY,8711
scipy/spatial/tests/data/pdist-minkowski-3.2-ml-iris.txt,sha256=N5L5CxRT5yf_vq6pFjorJ09Sr-RcnrAlH-_F3kEsyUU,178801
scipy/spatial/tests/data/pdist-euclidean-ml.txt,sha256=rkQA2-_d7uByKmw003lFXbXNDjHrUGBplZ8nB_TU5pk,3041
scipy/spatial/tests/data/pdist-cityblock-ml-iris.txt,sha256=UCWZJeMkMajbpjeG0FW60b0q-4r1geAyguNY6Chx5bM,178801
scipy/spatial/tests/data/pdist-double-inp.txt,sha256=0Sx5yL8D8pyYDXTIBZAoTiSsRpG_eJz8uD2ttVrklhU,50000
scipy/spatial/tests/data/pdist-cosine-ml.txt,sha256=P92Tm6Ie8xg4jGSP7k7bmFRAP5MfxtVR_KacS73a6PI,3041
scipy/spatial/tests/data/pdist-cityblock-ml.txt,sha256=8Iq7cF8oMJjpqd6qsDt_mKPQK0T8Ldot2P8C5rgbGIU,3041
scipy/spatial/tests/data/pdist-seuclidean-ml.txt,sha256=YmcI7LZ6i-Wg1wjAkLVX7fmxzCj621Pc5itO3PvCm_k,3041
scipy/spatial/tests/data/pdist-boolean-inp.txt,sha256=5Z9SMsXrtmzeUwJlVmGkrPDC_Km7nVpZIbBl7p3Hdc0,50000
scipy/spatial/tests/data/pdist-seuclidean-ml-iris.txt,sha256=37H01o6GibccR_hKIwwbWxGX0Tuxnb-4Qc6rmDxwwUI,178801
scipy/spatial/tests/data/pdist-chebyshev-ml-iris.txt,sha256=Yerj1wqIzcdyULlha-q02WBNGyS2Q5o2wAr0XVEkzis,178801
scipy/spatial/tests/data/random-bool-data.txt,sha256=MHAQdE4hPVzgu-csVVbm1DNJ80dP7XthJ1kb2In8ImM,6000
scipy/spatial/tests/data/pdist-hamming-ml.txt,sha256=IAYroplsdz6n7PZ-vIMIJ4FjG9jC1OSxc3-oVJdSFDM,3041
scipy/spatial/qhull_src/COPYING.txt,sha256=NNsMDE-TGGHXIFVcnNei4ijRKQuimvDy7oDEG7IDivs,1635
scipy/spatial/transform/_rotation.cpython-313-darwin.so,sha256=iW8l3sM3k8zOdtbrrMR76nhioqwBTAXZtF4koeLLTWI,766736
scipy/spatial/transform/__init__.py,sha256=vkvtowJUcu-FrMMXjEiyfnG94Cqwl000z5Nwx2F8OX0,700
scipy/spatial/transform/_rotation_spline.py,sha256=B1wmFTqR34W-CMAggNFvFgZwVrgP2v2iFVIzjnAxnA8,14076
scipy/spatial/transform/rotation.py,sha256=co5Bpny89EfCywilEeeLDvJPESBLrSXTCCJqRlfdYzg,556
scipy/spatial/transform/_rotation_groups.py,sha256=XS-9K6xYnnwWywMMYMVznBYc1-0DPhADHQp_FIT3_f8,4422
scipy/spatial/transform/tests/test_rotation_spline.py,sha256=g3prW5afu_yJxevIz2LMdRFYLfe8zq-3b6TMGw06Ads,5105
scipy/spatial/transform/tests/test_rotation_groups.py,sha256=V6DiLWvJsrdklhS-GlzcA9qEy0cTQpwaNR-7vkhBt1M,5560
scipy/spatial/transform/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/spatial/transform/tests/test_rotation.py,sha256=Aiyb9c3hunWc6bk6TtjQzjPDJCvgepZcgWhgH4NG1jw,69979
scipy/signal/waveforms.py,sha256=jfOXW7kgtGdh1nrMo1YLAh79W_Ln3WgzEN2esrp70wE,599
scipy/signal/_ltisys.py,sha256=sOxEME3e4217x6gFg7anY08p4CWoTS0jm6Np9IpsTM4,118051
scipy/signal/spline.py,sha256=S54RVqPeA7nnGzLgICi-2rl3Ei3roPaDAJ6ihTeZSwk,747
scipy/signal/_spectral_py.py,sha256=h0BILp8mj4Txrj7aNC3GWNviR8oKpxTNBHd-vgoGCqM,86897
scipy/signal/_sigtools.cpython-313-darwin.so,sha256=PyHngrfbX2fNes2cgebemD4nSo08qNBlJC93fo_47Rc,121568
scipy/signal/_savitzky_golay.py,sha256=AahANBsLy8d6FKmVgteGiAw1l_4wWWItZYSyOVnj_nk,13447
scipy/signal/signaltools.py,sha256=I7U_hMuMf02zpdNi0LcPogucTDf0nUVUSkMZ1eAoq3E,1038
scipy/signal/_wavelets.py,sha256=QTjAp83C2V2sxIkUsITWLw3ceIRkmBJ5CYtwW_3szCU,873
scipy/signal/_waveforms.py,sha256=Ca551WqyDWTrQrQ4hOwHl2dpHS1FSWg_SKyz1XObQrU,23089
scipy/signal/_signaltools.py,sha256=HioDs7paXI1cUu9gWsRQ6ZkL6h_x28q4NS3WJ4OANvY,176450
scipy/signal/bsplines.py,sha256=G1sa6en1z_41sU7ckRY8-flJjUKSqJJihaxBlwzUd3s,651
scipy/signal/_upfirdn_apply.cpython-313-darwin.so,sha256=zy7BnOYEhrU8YQ5r3vWUDZaAcBzcsQoodT_AjHZdmD4,296120
scipy/signal/__init__.py,sha256=tcYF8m39SxVh_JUIRVh8BdupHM3Gz8V6aJ_Y1Xorptg,13479
scipy/signal/_arraytools.py,sha256=k3kHbl9RzcqsyftIYSFJZvJFL4zlcMAHyaRFUkFxOXY,8294
scipy/signal/_filter_design.py,sha256=4k8U0EV4ySo5c5NsvLkleFftDomEBRdl7gg1qdGBn4s,187997
scipy/signal/_max_len_seq.py,sha256=8QkMWoYY3qy3bCKfsuXaS93Bnb2zd-ue6j5i5-3_hi0,5060
scipy/signal/wavelets.py,sha256=7pA7HVMiXwG4fZZ0Q4nzz47hWWALMTYtxwGrIqV3bNE,510
scipy/signal/_czt.py,sha256=t5P1kRCM3iw3eCaL9hTgctMfQKezkqnjbghLjCkffQE,19445
scipy/signal/lti_conversion.py,sha256=6uQ1qaT7XI75DoFmtRqRS94Hkpm-Qvy66CRNhmQ-Lbw,639
scipy/signal/_max_len_seq_inner.cpython-313-darwin.so,sha256=4YOyjawWRvRVatLcnU2vk-w_NuQLfmbWdUHq3pi5qms,76008
scipy/signal/_sosfilt.cpython-313-darwin.so,sha256=TAsH93T4kTPuNG4u73Zd69DR6yB3b4_0yVKAFih1XU4,243776
scipy/signal/_short_time_fft.py,sha256=7rAcfvEUEoaS_KUZ8d7Esh7f--emMS3K9KB5HheJF1o,76306
scipy/signal/_spline.pyi,sha256=9tWZQCI7D84ONLwICZG6psBGtwKxAvLF7JaZ1tQUKoY,948
scipy/signal/_peak_finding.py,sha256=e9vpWL98OQ9Ik1f7gwLl4d5feTAiyLwPm_yarJq3T_8,48856
scipy/signal/filter_design.py,sha256=EyHs8OX4mdeUi6e3Zf7IWuz6r5Re2eR_t0Bi10JuntM,1112
scipy/signal/_spline_filters.py,sha256=t1HWc3YEhDu6AtXo8z1CLTkFYpcbYvpOIRIMPiRMEGM,24487
scipy/signal/spectral.py,sha256=RA3jj6AWV6ptNwXfpVrbuyxxed8P7nWw8bLsD0iZIgw,662
scipy/signal/_lti_conversion.py,sha256=ZLlxEy1TrxvSXvAeDDSxgvKHHv5_lXxxJUjwIgbfpQE,16057
scipy/signal/_fir_filter_design.py,sha256=LEazCRvJAG9fyirZDqEnrgUpyv3ukl0r_SAOlUNQQw4,49741
scipy/signal/_upfirdn.py,sha256=ODSw2x1KHXN0vdKHm4vnovZxkoafcwIdUek0N8Edu5g,7882
scipy/signal/ltisys.py,sha256=TFul9jyL0ujEIchiOnDdIiJKIXZ8SSgOV066DvmX_QA,869
scipy/signal/fir_filter_design.py,sha256=0BxZF7tqewVQ4J1u-Ls-DZfC25rIcizwr9v6WaxkS0k,640
scipy/signal/_spline.cpython-313-darwin.so,sha256=vwq1BJKnxwvJLJW4D5WTJ5UPlrI1e9Ejkp9brGOQpKk,69744
scipy/signal/_peak_finding_utils.cpython-313-darwin.so,sha256=W8e97MfaGY9Q27nCUtdHqwGUNzV8qE3sRM7ozVa2xuk,227592
scipy/signal/tests/test_signaltools.py,sha256=F535YvwUJtCzLe4j7FpztU0zsVSP-rnoeAU0zvid0Es,153468
scipy/signal/tests/test_bsplines.py,sha256=_BZQE4CMyBfe0xG5QlWM8ckD5LNUADTY6CsGW1_0nxo,15926
scipy/signal/tests/test_max_len_seq.py,sha256=JzfWWN4n6FO9Axw6H6xWrWyc21LlkqMwkGl23f-V664,3318
scipy/signal/tests/test_wavelets.py,sha256=42yMux80J-K7Ue9QLnzN84U9K3j2GRdywMxGpbLldeM,2145
scipy/signal/tests/test_splines.py,sha256=mSCnwez3Qj3RBRYmyIBX7KGOf-tItiz0pU29GaVTsOA,14705
scipy/signal/tests/test_fir_filter_design.py,sha256=BTl7u38PhxS-j3DZwZ0hv7c_LsUKPfNuN8-KlPgV_yc,27732
scipy/signal/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/signal/tests/test_cont2discrete.py,sha256=0GgOVxKDnQRSN935P5N5A7qu3bm4iyp0Iz7qMs6vxTY,14672
scipy/signal/tests/test_ltisys.py,sha256=wU2ZC7E-lKDQ23_1Uvbem3PA_oNayRvzyccIaUqJbnc,45070
scipy/signal/tests/_scipy_spectral_test_shim.py,sha256=WalP9LfyXaXTQUW3mG8yhA3wJQ8E6edOjUsH4kSC2us,19640
scipy/signal/tests/test_short_time_fft.py,sha256=SmRhz7J_mwT0ophsVZxWgfm9fYmRr_4cRaXpRJZixC4,36362
scipy/signal/tests/test_dltisys.py,sha256=WEs5DsDSKQDm4H7deYr6lCUvm8TkiFd9S4SJIluRWfg,21483
scipy/signal/tests/test_filter_design.py,sha256=E_N744-VArOKv_gm4b6rdUmY5G4KB8jFPyVi7qDKhA8,198209
scipy/signal/tests/test_array_tools.py,sha256=QN4SGbtxDSP2MFvyYl00RasYYyNF4A1g8Y6_1Sij7YQ,3589
scipy/signal/tests/test_waveforms.py,sha256=XEQVDE7FRDH-wPOyBv7LQhSbmvXR45gnBNbpWr0925I,12985
scipy/signal/tests/test_peak_finding.py,sha256=ZSybjXxgtO3Go-l9S8d3NMdCR_wgKMllEivr8NDjyRo,36076
scipy/signal/tests/test_savitzky_golay.py,sha256=Tq17JiZJu2_nL9Q2T-7jql_MuDinKeAKqvtTiqBx87U,12503
scipy/signal/tests/test_result_type.py,sha256=F48EQGbFfQfMwcnt-sMofHGNHVTbHntbMlgoeS2vYcY,1573
scipy/signal/tests/test_windows.py,sha256=7KGQsexeNiI50RFjFnw4kr1tqigP-WFoGLFHK1Ygt5o,40990
scipy/signal/tests/test_upfirdn.py,sha256=B90gfpfFCe4EqsGm9hViKM2NtneNYfsxZR2PG8johHo,11323
scipy/signal/tests/test_spectral.py,sha256=W-x8s27sIrMd5jdLlUI1WfqGauYpeZSzWJGtV1ty_wY,78699
scipy/signal/tests/test_czt.py,sha256=2-kcWyadICVl_mF0vbq1KYii-rYMtZiuiOSb6HkYn7w,7156
scipy/signal/tests/mpsig.py,sha256=DHB3eHB0KYA-E0SBebKG36YLk-T5egbwwryne3RwIHM,3308
scipy/signal/windows/__init__.py,sha256=BUSXzc_D5Agp59RacDdG6EE9QjkXXtlcfQrTop_IJwo,2119
scipy/signal/windows/windows.py,sha256=FI6w8mt0V1221Rqv3Do3LuWRWrtKo3hYYTvpB_5UB1c,839
scipy/signal/windows/_windows.py,sha256=Scga4uJiDNUrH-p3ddILShNzXPmSOaA0Zvc6GOVyy6w,83594
scipy/stats/_multivariate.py,sha256=V_ArfvakTKERdhchS5vob52fOnCPHqLMYcbS0FixhOY,249240
scipy/stats/_covariance.py,sha256=g0oXQfcjugq9YpJhbmUECSOqYqPqsuDBD_69r_oGRDU,22524
scipy/stats/_constants.py,sha256=mBeJgvWcDZBmPFStDNEjlzeZY3aMDMCHWoj7dCmgugQ,1002
scipy/stats/_result_classes.py,sha256=_ghuGdpFsCMuEmnfHg1AeorR-fASc77ACXYWEmQzXjI,1085
scipy/stats/_binned_statistic.py,sha256=ATvrikTtX6zW8FKbjpV7O7IvAKSCBBLQSH1JKFR9R7Q,32702
scipy/stats/_survival.py,sha256=JexV_eUz0H_2QSwpido_M_LJr4mkODmhHVwjzFXjgj8,25939
scipy/stats/_mgc.py,sha256=iImSUbFmYh_7Ouap70PFP6O6CVpUylf5y44z33j3obg,21359
scipy/stats/_sensitivity_analysis.py,sha256=rSzMU4dmjN_zL-bt8tcxTTQbpRxNZuKrKn46zQtJyJc,25041
scipy/stats/_mvn.cpython-313-darwin.so,sha256=Fl8JV5GxZhXje1i1qi5HQx55ddvWV02aEHJWjX7G0QM,124688
scipy/stats/_censored_data.py,sha256=Ts7GSYYti2z-8yoOJTedj6aCLnGhugLlDRdxZc4rPxs,18306
scipy/stats/_distr_params.py,sha256=bD2Sdq0etEh0NYfi3-vFM-C7PevQfH0dRLbNnXeOtYY,9052
scipy/stats/_wilcoxon.py,sha256=wq_2sPwuiVA1kAFWJw3yegFp0TP5WVACPkYiTMrDs9U,9382
scipy/stats/_stats_py.py,sha256=AbZl_rpQP9U2hNAMpvMiVQ-kHUFOCdpIKrl_SNZLils,417517
scipy/stats/_tukeylambda_stats.py,sha256=eodvo09rCVfcYa1Uh6BKHKvXyY8K5Zg2uGQX1phQ6Ew,6871
scipy/stats/_qmvnt.py,sha256=oKf0JU2bY9_oePM-sLMD_xowKjMdlXFYR5c1veeuWKw,18769
scipy/stats/_warnings_errors.py,sha256=MpucxNFYEDytXh7vrZCMqTkRfuXTvvMpQ2W_Ak2OnPk,1196
scipy/stats/_relative_risk.py,sha256=5zeYBMshYwtomiLTkaXc1nmWYD0FsaQNjf0iuDadtSc,9571
scipy/stats/_stats_mstats_common.py,sha256=9SFbzUBOf6QpTwCiRkyXIlKAlm6B9uC8lv_VXSsiPzo,11557
scipy/stats/_qmc_cy.cpython-313-darwin.so,sha256=2MMkcw9E7pDGOrr8CQUhnvCMqoa0hxEYb9eukjvYXiQ,236896
scipy/stats/_new_distributions.py,sha256=4QuIqw-_QwJeIPsLDzFNDZBIpD7mTx4dwvEwn_5uoJk,13239
scipy/stats/_sobol.pyi,sha256=TAywylI75AF9th9QZY8TYfHvIQ1cyM5QZi7eBOAkrbg,971
scipy/stats/_odds_ratio.py,sha256=zZvZsD7ftKeWUrypXeUapcNoq006XldVAkMMC3RLbWE,17005
scipy/stats/_binomtest.py,sha256=aW6p-vRkv3pSB8_0nTfT3kNAhV8Ip44A39EEPyl9Wlc,13118
scipy/stats/mvn.py,sha256=pOcB_Dd_DHpfbYnuJKq-wqmNNGCun1M0294xK1bX0KQ,498
scipy/stats/_ksstats.py,sha256=JsUipfbLw0TMrmUpkvHY06Rk_eXT0l7WemK9xhVdLiA,20139
scipy/stats/_resampling.py,sha256=46DA0dE1CTlXR-vVBenghqptFL7wDadr2g0CKp4IMQs,104295
scipy/stats/_page_trend_test.py,sha256=OvisWd3E6CF7rdFRGv46HWOfJlyHalMITt5iJPzE8LI,18987
scipy/stats/biasedurn.py,sha256=ECfilE4KrIhU2sK-KWtr8yxqthfVsyz_-o4F2TnMXU4,431
scipy/stats/kde.py,sha256=8ZThSc3lz-l1Gb2jzIvy1J87_HTd7eXzxuPLClVpo7c,516
scipy/stats/_common.py,sha256=4RqXT04Knp1CoOJuSBV6Uy_XmcmtVr0bImAbSk_VHlQ,172
scipy/stats/_mstats_basic.py,sha256=GXFCsZtbKg6kJuvXSCGRxhtme-dfzBLvl2r-g2UWGGM,122939
scipy/stats/_biasedurn.pxd,sha256=bQC6xG4RH1E5h2jCKXRMADfgGctiO5TgNlJegKrR7DY,1046
scipy/stats/__init__.py,sha256=CUo1rk_ClMcxEIobb_XxhRWZi1IZ--FkHazykYw8a6Q,18680
scipy/stats/mstats_extras.py,sha256=925lNnnf_NTRoyAnXql-k9syzhv7MF6T2kPGsdE2FHc,721
scipy/stats/_qmc.py,sha256=sJfB3Jz8unPDBe_TPN5qm1YK4emQ7lJN7iQ2_vGBO9E,107502
scipy/stats/_discrete_distns.py,sha256=nYPH9LKlqC0q_RFMitD4XEsP9F0pfnM-B1JnJtLwACw,65095
scipy/stats/_biasedurn.cpython-313-darwin.so,sha256=PIFHJTANX47L0Q8tBhDaR8CujrNhX-VqZkztWZiDsrw,176480
scipy/stats/_crosstab.py,sha256=djdU7xCQ-513VlxFEOvLN8oaY4QyUPHDJHWlilhyEVA,7351
scipy/stats/distributions.py,sha256=9Kt2fyTohorJcf6a7M9DYH8Nu4jEU66nKP01cRhKmuE,859
scipy/stats/_kde.py,sha256=EAMQrO4MRwIcdOuQ1v-R6TP5IpAo_kZThwTEmRj8v7M,25089
scipy/stats/_hypotests.py,sha256=gDsPkfLiTH3oCeBL_FPOcC1Gvz53SHuda2a3YPE9hr4,79170
scipy/stats/_stats_pythran.cpython-313-darwin.so,sha256=eO5Cxm30_KuKXG_gnUyEltV3PfXCxoKT4h0YFVtfC0A,166504
scipy/stats/_sampling.py,sha256=YJ1mG2tkXW4Em-virElY-cNzMXn8lHbOxNxujqDsPY0,46408
scipy/stats/_multicomp.py,sha256=x9XBSCbTWl4V-hUZ_YaMYZ5smpE95qBCUic6yYygnpA,16836
scipy/stats/mstats.py,sha256=aRbrykjrvl-qOBkmGjlFMH4rbWYSqBBQHReanSAomFg,2466
scipy/stats/_sobol.cpython-313-darwin.so,sha256=dbuSYAXqYiTavaKBRTl01T6W_M5TviK9tNPZm-InJTI,312208
scipy/stats/_distn_infrastructure.py,sha256=nfk3LYe26PjZzrTug-ZDKKCI-qsmTsQCfj99-fR9Tvw,151588
scipy/stats/stats.py,sha256=EgWjDdnlfCRKJymUcBDvMvPn0ZLO3G_ml1XJ7wvMbCI,1512
scipy/stats/_mstats_extras.py,sha256=VMtwkTOFc3eBGFHiqO0cJjr98PC0fc2EIO_oKGIQJQo,16366
scipy/stats/sampling.py,sha256=VYwxxGosFs-T3qdCmdw4tJYEFLlegwj-JgDin7iwndE,1939
scipy/stats/_continuous_distns.py,sha256=QBGjt-kwhjmzU3-XZtnkJSXy4KC6iIk7De_k3TZCFf4,407685
scipy/stats/qmc.py,sha256=b6gLkc_FSm11Ssb9uIai4XxLk4XL_qqK6Jc2k4RSeN0,11703
scipy/stats/_stats.pxd,sha256=T_7IrDqgIahKMECV5WAtxtsoV91XBVRM359kAXPIhww,709
scipy/stats/morestats.py,sha256=GdMXz4MSuPp7hsff_DoijVtFsCEyy6J3_M7BITKGiP4,973
scipy/stats/_sobol_direction_numbers.npz,sha256=SFmTEUfULORluGBcsnf5V9mLg50DGU_fBleTV5BtGTs,589334
scipy/stats/_correlation.py,sha256=TKenq2UmJ6gMligjczL1nTIXgUShprfYyBc23lhTCuo,7911
scipy/stats/_ansari_swilk_statistics.cpython-313-darwin.so,sha256=FeXF9U6NEUkhxgQwkTzmgXN8EnesnWBJsvYUukrUFhQ,211600
scipy/stats/_stats.cpython-313-darwin.so,sha256=7RgWxF-gxMjFTk8ToxbT8dpx-pJLOxuas22UnSawbs4,538464
scipy/stats/mstats_basic.py,sha256=PjgL37PCPwiDx_ptqnmKXc1W3QGlRjjPrG0nI5FA4So,1394
scipy/stats/contingency.py,sha256=psNLzIB1A00rE4U9LwdYyt6XpYZlPRBCqQSMOEjHH04,18649
scipy/stats/_axis_nan_policy.py,sha256=vtqhfxpJUrpD9GETwnB1HN7fe2NLIPt8QkGXjr3VPa8,31788
scipy/stats/_distribution_infrastructure.py,sha256=yXlXMuwpT_MykLntuBKbNd4EmGjPe40e0HqC9Ia2PzI,203772
scipy/stats/_variation.py,sha256=2DfKIrosnZ68BzG7BLJNAAR692BN0SvZhlBs6M86l5U,4652
scipy/stats/_qmc_cy.pyi,sha256=xOpTSlaG_1YDZhkJjQQtukbcgOTAR9FpcRMkU5g9mXc,1134
scipy/stats/_probability_distribution.py,sha256=xcvEl_eux4p8SSRKbTpb3Ipmfs9XAx522RK1ebkKiks,61504
scipy/stats/_mannwhitneyu.py,sha256=LQII0f5CF4-OfWXqBuP4uPjNJ8IuVgPp04itqacy1EA,19330
scipy/stats/_fit.py,sha256=PmLg5oE25gnOIHVV-4U-nfUEsKdfgac4M9OaBSjKrow,59747
scipy/stats/_morestats.py,sha256=0Q1FJqhMJICguWL7HbrRKwwCyqfZUTLN7WxOeeKa2-0,170393
scipy/stats/_bws_test.py,sha256=XQMGiLMPKFN3b6O4nD5tkZdcI8D8vggSx8B7XLJ5EGs,7062
scipy/stats/_entropy.py,sha256=hMlhLViQos20KYpBwmQf9fSfmbMzoCluF4uRg7yKxTc,15831
scipy/stats/_levy_stable/levyst.cpython-313-darwin.so,sha256=NBiZ-fdXIFuJejvKhk1coF0hG5E_7Sz_ilQG6i9RIK0,78064
scipy/stats/_levy_stable/__init__.py,sha256=J2Nw8Ye0e52Q9cC4o08H56QnLd1Frp_fB3WuxInP6II,45986
scipy/stats/_rcont/__init__.py,sha256=dUzWdRuJNAxnGYVFjDqUB8DMYti3by1WziKEfBDOlB4,84
scipy/stats/_rcont/rcont.cpython-313-darwin.so,sha256=9P5q5lx7FzinIN6DDNsnVMjHatQe3vLgkp1PI2df7r0,208536
scipy/stats/tests/test_variation.py,sha256=0kSCLGFi7sgEwLf6hf1LRSHCRgLNANQ5SMigh_zxv5s,9202
scipy/stats/tests/test_continuous_fit_censored.py,sha256=7hu1sSo9hhh0g9pmPMmjj2BI2rkxvA1h20XdMYZeyog,24188
scipy/stats/tests/test_mstats_basic.py,sha256=3CUi7mahUSPQCqYBZqnVKMy7CcQ_kaL2au6KGwVyWgc,87293
scipy/stats/tests/test_morestats.py,sha256=HtMQ_acaYaA_UH9RFutqwOuEspSKdi685IW4FQ8b9CE,141447
scipy/stats/tests/test_continuous.py,sha256=xqtMvLk_0evu7JXfD3m99XB4aGOb86xfZ_vt0LRTo90,79370
scipy/stats/tests/test_stats.py,sha256=g9zLhnOaYgJjeu84fdOFuWFRL8jwz5GBL_WJogVy8_A,413686
scipy/stats/tests/test_discrete_distns.py,sha256=OZcCMkh7FgabSKw_N0G3ZT_dYolSqnq3DRXjvHpFKso,25261
scipy/stats/tests/test_continuous_basic.py,sha256=DUoZd6JkrtNUdOw73pO7BZRPUQUlxXRV9nGag-HzDh8,42878
scipy/stats/tests/test_fast_gen_inversion.py,sha256=AD3Ae0tiT9mn2rljErvfCEfEG0TlAZfL_nufQuhnDBc,15935
scipy/stats/tests/test_mstats_extras.py,sha256=CCexzT1lksTG_WvGvHn6-CuWd_ZXoFviNGnBZd_hE7Y,7297
scipy/stats/tests/test_survival.py,sha256=Wmig-n93Y2wCuye9btK4QqXwUAdzF0xR_MO9iYZARjU,21958
scipy/stats/tests/test_fit.py,sha256=XN7xEz1RbTNqWhStlOGXJEn4wITaTS5Fe0vHvyHhCVk,48875
scipy/stats/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/stats/tests/test_multivariate.py,sha256=-QIaPK97iADoy9mOKhOT7IZfp1Ap1Rzho6iBlObYcP4,160290
scipy/stats/tests/test_hypotests.py,sha256=VTxuKnCwFCd3jPzkPJEjSk_v0Gd9yDA1skGXm2fCeIc,79978
scipy/stats/tests/test_multicomp.py,sha256=s5mL9NQMvD4khQ12n2_maXKX9Q5pI0HFjcaYMZyhcJ0,17826
scipy/stats/tests/test_axis_nan_policy.py,sha256=gY4fbPZ5CQcLh6ThXVKBPIkhODT_9YobZ29x5SDREps,58567
scipy/stats/tests/test_odds_ratio.py,sha256=ZII-yvP_vhuaNa3qPB0Q5lh9yzRF-08ZcdkAwuu5E94,6727
scipy/stats/tests/test_tukeylambda_stats.py,sha256=6WUBNVoTseVjfrHfWXtU11gTgmRcdnwAPLQOI0y_5U8,3231
scipy/stats/tests/test_binned_statistic.py,sha256=WE5KdJq4zJxZ1LuYp8lv-RMcTEyjuSkjvFHWsGMujkM,18814
scipy/stats/tests/test_contingency.py,sha256=00QIN99yybM_HhrLf8kck85gWPUAQmYIKI7XnVzPF94,10937
scipy/stats/tests/common_tests.py,sha256=YN4v0L134k9B-QphMZECDUv5COfjGILIaQ9Su5qV8Zs,12434
scipy/stats/tests/test_mgc.py,sha256=x8e8Y1xmBeYZSc9IXoJVSJWudUD8CCbFPe5lmCghfrw,7961
scipy/stats/tests/test_correlation.py,sha256=I_iO0q5jqRa7yWMexR5hDdoeSuJS73HIUjOzzZUpBxE,3507
scipy/stats/tests/test_distributions.py,sha256=w9LvKcRjZq-ezZS4g6TL2VxlqJOOBL2k8jDWXuZKwac,412126
scipy/stats/tests/test_kdeoth.py,sha256=3SqPL5iUxqFx-GgI0g9TYVdUhnTSX3sCnJZirUrol5E,20473
scipy/stats/tests/test_entropy.py,sha256=bQ2Rj43zrILlrWDw7tAzDntQNC-t8RhDemXt2HAdfS4,13953
scipy/stats/tests/test_rank.py,sha256=TL5pC9C5dULvoYOf4droiEmaSglSOlMZ4h88yzLRHy4,11793
scipy/stats/tests/test_discrete_basic.py,sha256=8STriXyCJE6f0CevuI4PYbfISory6pi1KQdqpMShtzg,21022
scipy/stats/tests/test_sensitivity_analysis.py,sha256=nNF_B6Zl5YxmvppI8TEPOGroDsbgyLTF6jBmdJH2AUw,10678
scipy/stats/tests/test_relative_risk.py,sha256=jzOGNQ2y9_YfFnXiGAiRDrgahy66qQkw6ZkHgygCJMA,3646
scipy/stats/tests/test_resampling.py,sha256=OQQ31s1EviAaab7pcTc2jQS8rWCTg9-kdaxRapqRqVs,82429
scipy/stats/tests/test_censored_data.py,sha256=pAQfSHhmcetcxoS1ZgIHVm1pEbapW7az7I-y_8phb5w,6935
scipy/stats/tests/test_sampling.py,sha256=icj26ffwNkFRje6jpWQ2HnPr57nfWUSiP8bwU8mZIgo,54540
scipy/stats/tests/test_qmc.py,sha256=Y_X-H7dXX88Bl-YaxYLtvzOoNpLYuvl2k-4nNpsjRXU,57529
scipy/stats/tests/test_crosstab.py,sha256=2zqnoWW70MkvFjxAQlpW4vzWI624rcYLAlAVf7vZ9DU,3906
scipy/stats/tests/data/rel_breitwigner_pdf_sample_data_ROOT.npy,sha256=7vTccC3YxuMcGMdOH4EoTD6coqtQKC3jnJrTC3u4520,38624
scipy/stats/tests/data/_mvt.py,sha256=OvFCmMqI74DWIgo32UV55dP1nzvFvYBSyYcmKJes9pI,6905
scipy/stats/tests/data/fisher_exact_results_from_r.py,sha256=BKxPAi4h3IOebcZYGxCbutYuAX0tlb40P0DEkfEi918,27349
scipy/stats/tests/data/studentized_range_mpmath_ref.json,sha256=icZGNBodwmJNzOyEki9MreI2lS6nQJNWfnVJiHRNRNM,29239
scipy/stats/tests/data/jf_skew_t_gamlss_pdf_data.npy,sha256=JU0t7kpNVHuTMcYCQ8b8_K_9JsixBNCNT2BFp2RbO7o,4064
scipy/stats/tests/data/levy_stable/stable-Z1-cdf-sample-data.npy,sha256=zxjB8tZaIyvyxxISgt8xvyqL6Cevr8TtgQ7TdFfuiYo,183728
scipy/stats/tests/data/levy_stable/stable-Z1-pdf-sample-data.npy,sha256=_umVErq0zMZWm0e5JOSwNOHNurViT6_H4SBki9X3oSg,183688
scipy/stats/tests/data/levy_stable/stable-loc-scale-sample-data.npy,sha256=88cZ7dVDH7nnuey20Z48p6kJUpi9GfImaFsPykDwwHM,9328
scipy/stats/tests/data/nist_anova/SmLs08.dat,sha256=qrxQQ0I6gnhrefygKwT48x-bz-8laD8Vpn7c81nITRg,59228
scipy/stats/tests/data/nist_anova/SmLs09.dat,sha256=qmELOQyNlH7CWOMt8PQ0Z_yxgg9Hxc4lqZOuHZxxWuc,577633
scipy/stats/tests/data/nist_anova/SiRstv.dat,sha256=x9wJ2g1qnzf4DK_w9F_WiOiDMDEg4td2z6uU77G07xM,1947
scipy/stats/tests/data/nist_anova/SmLs02.dat,sha256=nCPyxRk1dAoSPWiC7kG4dLaXs2GL3-KRXRt2NwgXoIA,46561
scipy/stats/tests/data/nist_anova/SmLs03.dat,sha256=6yPHiQSk0KI4oURQOk99t-uEm-IZN-8eIPHb_y0mQ1U,451566
scipy/stats/tests/data/nist_anova/SmLs01.dat,sha256=KdnJedRthF7XLA-w7XkIPIMTgzu89yBAMmZA2H4uQOQ,6055
scipy/stats/tests/data/nist_anova/AtmWtAg.dat,sha256=Qdd0i7H4cNhAABfFOZPuplhi_9SCquFpO-hNkyRcMD8,3063
scipy/stats/tests/data/nist_anova/SmLs04.dat,sha256=fI-HpgJF9cdGdBinclhVzOcWCCc5ZJZuXalUwirV-lc,6815
scipy/stats/tests/data/nist_anova/SmLs05.dat,sha256=iJTaAWUFn7DPLTd9bQh_EMKEK1DPG0fnN8xk7BQlPRE,53799
scipy/stats/tests/data/nist_anova/SmLs07.dat,sha256=QtSS11d-vkVvqaIEeJ6oNwyET1CKoyQqjlfBl2sTOJA,7381
scipy/stats/tests/data/nist_anova/SmLs06.dat,sha256=riOkYT-LRgmJhPpCK32x7xYnD38gwnh_Eo1X8OK3eN8,523605
scipy/stats/tests/data/nist_linregress/Norris.dat,sha256=zD_RTRxfqJHVZTAAyddzLDDbhCzKSfwFGr3hwZ1nq30,2591
scipy/stats/_unuran/unuran_wrapper.pyi,sha256=TT9P08hsVQu6W7giss8kweV-FKcLffwZO9gyxmbpi2c,5588
scipy/stats/_unuran/unuran_wrapper.cpython-313-darwin.so,sha256=EIdB2x820SIWMDY-k0gq8suUw-Popbtge-fDepOxNHc,669720
scipy/stats/_unuran/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy-1.15.3.dist-info/INSTALLER,sha256=MplGNgtSIPHiaynfY81kpB37_zNbmvf5N0NBrMdOvpI,12
scipy-1.15.3.dist-info/RECORD,,
