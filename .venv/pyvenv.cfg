home = /opt/homebrew/Cellar/python@3.13/3.13.3/bin
implementation = CPython
version_info = 3.13.3.final.0
virtualenv = 20.30.0
include-system-site-packages = false
base-prefix = /opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13
base-exec-prefix = /opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13
base-executable = /opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/bin/python3.13
prompt = emergent-models-py3.13
